/**
 * SEO Strategist Agent - KaibanJS Content Team
 * Specializes in keyword research, SEO strategy, and content optimization
 */
import { Agent, Task } from 'kaibanjs';
import { SEOAnalyzer } from '../tools/seo-analyzer.js';

export const seoStrategistAgent = new Agent({
  name: 'SEO Strategist',
  role: 'Senior SEO Specialist',
  goal: 'Develop comprehensive SEO strategies and optimize content for maximum search engine visibility',
  backstory: `You are a seasoned SEO expert with over 8 years of experience in search engine optimization and content marketing. Your expertise includes:

  - Advanced keyword research and analysis
  - SERP analysis and competitive intelligence
  - On-page and technical SEO optimization
  - Content gap analysis and opportunity identification
  - Search intent understanding and mapping
  - Local SEO and international SEO strategies
  - SEO performance tracking and analytics
  - Algorithm updates and ranking factor analysis

  You stay current with Google's algorithm changes and best practices, ensuring all content follows the latest SEO guidelines for E-A-T (Expertise, Authoritativeness, Trustworthiness) and helpful content standards.`,
  
  tools: [SEOAnalyzer],
  
  llmConfig: {
    provider: 'openrouter',
    name: 'anthropic/claude-3-haiku',
    config: {
      temperature: 0.2, // Low temperature for analytical accuracy
      max_tokens: 2000
    }
  }
});

// SEO Strategy Tasks
export const developKeywordStrategy = new Task({
  description: `Develop a comprehensive keyword strategy for the topic: {topic}

  Your keyword strategy should include:
  1. **Primary Keywords**: 1-3 main target keywords with high search volume and relevance
  2. **Secondary Keywords**: 5-10 supporting keywords that complement the primary terms
  3. **Long-tail Keywords**: 10-15 specific, lower-competition phrases
  4. **LSI Keywords**: Semantically related terms that Google associates with the topic
  5. **Question Keywords**: Common questions people ask about this topic
  6. **Commercial Keywords**: Terms indicating buying intent or commercial interest
  7. **Local Keywords**: Location-based variations if applicable

  For each keyword group, provide:
  - Search volume estimates
  - Keyword difficulty scores
  - Search intent classification (informational, navigational, transactional)
  - Competitive analysis
  - Recommended placement strategy (title, headers, body, meta)

  Use the SEO analysis tools to research competitors and identify content gaps.`,
  
  expectedOutput: `A comprehensive keyword strategy document containing:
  - Primary Keyword List (1-3 keywords) with metrics
  - Secondary Keyword List (5-10 keywords) with analysis
  - Long-tail Keyword Opportunities (10-15 phrases)
  - LSI and Semantic Keywords
  - Question-based Keywords for FAQ sections
  - Commercial Intent Keywords
  - Keyword Difficulty and Opportunity Assessment
  - Competitive Gap Analysis
  - Content Optimization Recommendations
  - Keyword Placement Strategy Map`,
  
  agent: seoStrategistAgent
});

export const analyzeSerpCompetitors = new Task({
  description: `Analyze SERP competitors and identify content opportunities for: {topic}

  Your competitor analysis should include:
  1. **Top 10 SERP Analysis**: Review the current top-ranking pages
  2. **Content Gap Identification**: Find topics competitors are missing
  3. **Content Quality Assessment**: Evaluate depth, accuracy, and user value
  4. **Technical SEO Review**: Analyze page speed, structure, and optimization
  5. **Content Format Analysis**: Identify successful content types and formats
  6. **Backlink Opportunities**: Find potential link-building prospects
  7. **Featured Snippet Opportunities**: Identify chances to capture position zero
  8. **User Intent Mapping**: Understand what users really want from search results

  Provide actionable recommendations for outranking competitors.`,
  
  expectedOutput: `A detailed competitor analysis report with:
  - Top 10 SERP Competitor Profiles
  - Content Gap Opportunities List
  - Content Quality Benchmark Analysis
  - Technical SEO Competitor Comparison
  - Successful Content Format Identification
  - Featured Snippet Opportunity Assessment
  - Backlink Prospect Analysis
  - User Intent Insights
  - Competitive Advantage Recommendations
  - Content Differentiation Strategy`,
  
  agent: seoStrategistAgent
});

export const createSeoOptimizationPlan = new Task({
  description: `Create a comprehensive SEO optimization plan for content about: {topic}

  Your optimization plan should cover:
  1. **On-Page SEO Elements**:
     - Title tag optimization (50-60 characters)
     - Meta description (150-160 characters)
     - Header structure (H1, H2, H3) with keyword placement
     - URL structure and slug optimization
     - Image alt text and optimization
     - Internal linking strategy
     - Schema markup recommendations

  2. **Content SEO Guidelines**:
     - Keyword density and placement recommendations
     - Content length optimization
     - Readability and user experience factors
     - Content freshness and update strategy
     - FAQ section optimization
     - Call-to-action placement

  3. **Technical SEO Considerations**:
     - Page loading speed requirements
     - Mobile optimization checklist
     - Core Web Vitals optimization
     - Structured data implementation
     - Canonical tag strategy

  Provide specific, actionable SEO recommendations that can be implemented during content creation.`,
  
  expectedOutput: `A comprehensive SEO optimization plan including:
  - Title Tag Templates and Examples
  - Meta Description Templates
  - Header Structure Blueprint
  - URL Structure Guidelines
  - Keyword Placement Map
  - Internal Linking Strategy
  - Image Optimization Checklist
  - Schema Markup Recommendations
  - Content Length and Structure Guidelines
  - Readability Optimization Tips
  - Technical SEO Checklist
  - Performance Benchmark Targets
  - SEO Success Metrics and KPIs`,
  
  agent: seoStrategistAgent
});

export const generateSeoBriefs = new Task({
  description: `Generate SEO content briefs for multiple content pieces around: {topic}

  Create detailed briefs for:
  1. **Main Pillar Content**: Comprehensive guide or cornerstone article
  2. **Supporting Articles**: 3-5 related subtopic pieces
  3. **FAQ Content**: Question-focused content pieces
  4. **Commercial Pages**: Service/product pages if applicable
  5. **Local Content**: Location-specific variations if relevant

  Each brief should include:
  - Target keywords and search intent
  - Recommended content structure
  - Key points to cover
  - Competitor analysis insights
  - SEO optimization requirements
  - Content format recommendations
  - Expected word count
  - Success metrics

  Focus on creating a content cluster strategy that establishes topical authority.`,
  
  expectedOutput: `A collection of SEO content briefs containing:
  - Pillar Content Brief (comprehensive guide)
  - Supporting Article Briefs (3-5 pieces)
  - FAQ Content Brief
  - Commercial Content Brief (if applicable)
  - Local Content Variations (if applicable)
  - Content Cluster Strategy Map
  - Internal Linking Blueprint
  - Topical Authority Development Plan
  - Content Calendar Recommendations
  - Success Metrics for Each Brief`,
  
  agent: seoStrategistAgent
});

export const auditContentSeo = new Task({
  description: `Perform SEO audit on content draft for: {topic}

  Your audit should evaluate:
  1. **Keyword Optimization**:
     - Primary keyword placement and density
     - Secondary keyword distribution
     - LSI keyword integration
     - Keyword stuffing check

  2. **On-Page Elements**:
     - Title tag effectiveness
     - Meta description quality
     - Header structure and hierarchy
     - URL optimization
     - Image optimization

  3. **Content Quality**:
     - Search intent alignment
     - Content depth and comprehensiveness
     - Readability and user experience
     - Content uniqueness and value
     - E-A-T factors (Expertise, Authority, Trust)

  4. **Technical Factors**:
     - Internal linking opportunities
     - External link quality
     - Schema markup needs
     - Mobile optimization
     - Page speed considerations

  Provide specific recommendations for improvement with priority levels.`,
  
  expectedOutput: `A comprehensive SEO audit report with:
  - Overall SEO Score (1-100)
  - Keyword Optimization Assessment
  - On-Page Element Review
  - Content Quality Evaluation
  - Technical SEO Checklist
  - Priority Improvement Recommendations
  - Implementation Guidelines
  - Expected Impact Assessment
  - Before/After Optimization Comparison
  - Final Approval Recommendations`,
  
  agent: seoStrategistAgent
});