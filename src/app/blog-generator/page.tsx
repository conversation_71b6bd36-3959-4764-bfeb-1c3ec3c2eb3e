'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { FileText, Search, Zap, ArrowLeft, Copy, Download, Sparkles, TrendingUp, Target, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import Link from 'next/link'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'

export default function BlogGeneratorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [formData, setFormData] = useState({
    topic: '',
    title: '',
    wordCount: 2000,
    tone: 'professional',
    targetKeyword: '',
    targetAudience: '',
    numResults: 10
  })
  
  // Multi-step states
  const [currentStep, setCurrentStep] = useState('form') // 'form', 'analyzing', 'generating', 'complete'
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')
  const [externalSources, setExternalSources] = useState<any[]>([])
  const [contentMetadata, setContentMetadata] = useState<any>(null)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [progressSteps, setProgressSteps] = useState<string[]>([])
  const [currentProgressStep, setCurrentProgressStep] = useState(0)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Enhanced progress tracking
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isAnalyzing && currentProgressStep < progressSteps.length - 1) {
      // Variable timing based on actual API phases
      const timings = [4000, 8000, 3000, 6000, 2000] // Realistic timings for each phase
      const currentTiming = timings[currentProgressStep] || 3000
      
      interval = setTimeout(() => {
        setCurrentProgressStep(prev => Math.min(prev + 1, progressSteps.length - 1))
      }, currentTiming)
    }
    return () => clearTimeout(interval)
  }, [isAnalyzing, currentProgressStep, progressSteps.length])

  // Progress tracking with estimated completion
  const getProgressPercentage = () => {
    if (currentStep === 'form') return 0
    if (currentStep === 'analyzing') return Math.min((currentProgressStep / progressSteps.length) * 70, 65)
    if (currentStep === 'generating') return 85
    if (currentStep === 'complete') return 100
    return 0
  }

  const getEstimatedTime = () => {
    if (currentStep === 'analyzing') return '3-4 minutes'
    if (currentStep === 'generating') return '1-2 minutes'
    return null
  }

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-pink-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  // Start analysis phase
  const handleAnalyze = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.topic) return
    
    setIsAnalyzing(true)
    setCurrentStep('analyzing')
    setProgressSteps([
      'Conducting Tavily search...',
      'Extracting content from top pages...',
      'Storing in knowledge base...',
      'Analyzing competitors...',
      'Generating insights...',
      'Creating optimized content...'
    ])
    setCurrentProgressStep(0)
    
    try {
      // Step 1: Run analysis
      const analysisResponse = await fetch('/api/blog-generator/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topic: formData.topic,
          targetKeyword: formData.targetKeyword,
          numResults: formData.numResults
        })
      })
      
      const analysisData = await analysisResponse.json()
      if (!analysisData.success) {
        alert('Analysis failed: ' + analysisData.error)
        setCurrentStep('form')
        setIsAnalyzing(false)
        return
      }
      
      // Step 2: Immediately start content generation
      setCurrentStep('generating')
      setIsGenerating(true)
      
      const generationResponse = await fetch('/api/blog-generator/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topic: formData.topic,
          targetKeyword: formData.targetKeyword,
          wordCount: formData.wordCount,
          tone: formData.tone,
          title: formData.title,
          targetAudience: formData.targetAudience,
          analysisId: analysisData.metadata.analysisId,
          analysisData: analysisData.data, // Pass the complete analysis data
          useKnowledgeBase: true,
          useCompetitiveAnalysis: true
        })
      })
      
      const generationData = await generationResponse.json()
      if (generationData.success) {
        setGeneratedContent(generationData.content)
        setExternalSources(generationData.externalSources || [])
        setContentMetadata(generationData.metadata || {})
        setCurrentStep('complete')
      } else {
        alert('Generation failed: ' + generationData.error)
        setCurrentStep('form')
      }
    } catch (error) {
      alert('Failed to generate content')
      setCurrentStep('form')
    } finally {
      setIsAnalyzing(false)
      setIsGenerating(false)
    }
  }

  // Reset to form
  const handleReset = () => {
    setCurrentStep('form')
    setGeneratedContent('')
    setExternalSources([])
    setContentMetadata(null)
    setFormData({
      topic: '',
      title: '',
      wordCount: 2000,
      tone: 'professional',
      targetKeyword: '',
      targetAudience: '',
      numResults: 10
    })
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedContent)
  }

  const downloadAsMarkdown = () => {
    const blob = new Blob([generatedContent], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${formData.topic.replace(/\s+/g, '-').toLowerCase()}.md`
    a.click()
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-pink-900/20 via-black to-rose-900/20" />
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-pink-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-rose-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="border-b border-white/10 bg-black/60 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </motion.button>
                </Link>
                <div className="flex items-center space-x-3">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-pink-600 to-rose-600">
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">Enhanced Blog Generator</h1>
                    <p className="text-gray-400">AI-Powered SEO Content with Competitive Analysis</p>
                    {getEstimatedTime() && (
                      <div className="flex items-center space-x-2 mt-1">
                        <p className="text-xs text-pink-400">Estimated time: {getEstimatedTime()}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Progress indicator */}
              {(currentStep !== 'form' && currentStep !== 'complete') && (
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <p className="text-sm text-white font-medium">
                      {currentStep === 'analyzing' && 'Researching & Analyzing...'}
                      {currentStep === 'generating' && 'Writing Content...'}
                    </p>
                    <p className="text-xs text-gray-400">{Math.round(getProgressPercentage())}% complete</p>
                  </div>
                  <div className="w-16 h-16 relative">
                    <svg className="w-16 h-16 transform -rotate-90">
                      <circle
                        cx="32"
                        cy="32"
                        r="28"
                        stroke="rgb(75, 85, 99)"
                        strokeWidth="4"
                        fill="none"
                      />
                      <circle
                        cx="32"
                        cy="32"
                        r="28"
                        stroke="rgb(236, 72, 153)"
                        strokeWidth="4"
                        fill="none"
                        strokeDasharray={`${2 * Math.PI * 28}`}
                        strokeDashoffset={`${2 * Math.PI * 28 * (1 - getProgressPercentage() / 100)}`}
                        className="transition-all duration-300"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-xs font-bold text-white">
                        {Math.round(getProgressPercentage())}%
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Global progress bar */}
          {getProgressPercentage() > 0 && getProgressPercentage() < 100 && (
            <div className="h-1 bg-gray-800">
              <motion.div
                className="h-full bg-gradient-to-r from-pink-500 to-rose-500"
                initial={{ width: 0 }}
                animate={{ width: `${getProgressPercentage()}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Form Panel */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                  <Sparkles className="w-5 h-5 mr-2 text-pink-400" />
                  Content Configuration
                </h2>
                
                <form onSubmit={handleAnalyze} className="space-y-6">
                  {/* Topic */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Blog Topic *
                    </label>
                    <input
                      type="text"
                      value={formData.topic}
                      onChange={(e) => setFormData({...formData, topic: e.target.value})}
                      placeholder="Enter your blog topic..."
                      required
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"
                    />
                  </div>

                  {/* Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Custom Title (Optional)
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      placeholder="Leave blank for AI-generated title"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"
                    />
                  </div>

                  {/* Word Count and Tone */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Word Count
                      </label>
                      <select
                        value={formData.wordCount}
                        onChange={(e) => setFormData({...formData, wordCount: parseInt(e.target.value)})}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all"
                      >
                        <option value={500}>500 words</option>
                        <option value={1000}>1,000 words</option>
                        <option value={1500}>1,500 words</option>
                        <option value={2000}>2,000 words</option>
                        <option value={3000}>3,000 words</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Tone
                      </label>
                      <select
                        value={formData.tone}
                        onChange={(e) => setFormData({...formData, tone: e.target.value})}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all"
                      >
                        <option value="professional">Professional</option>
                        <option value="casual">Casual</option>
                        <option value="conversational">Conversational</option>
                        <option value="authoritative">Authoritative</option>
                        <option value="friendly">Friendly</option>
                        <option value="academic">Academic</option>
                      </select>
                    </div>
                  </div>

                  {/* Number of Results */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Research Depth
                    </label>
                    <select
                      value={formData.numResults}
                      onChange={(e) => setFormData({...formData, numResults: parseInt(e.target.value)})}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all"
                    >
                      <option value={5}>5 sources (Fast)</option>
                      <option value={10}>10 sources (Recommended)</option>
                      <option value={15}>15 sources (Comprehensive)</option>
                    </select>
                  </div>

                  {/* Advanced Options */}
                  <button
                    type="button"
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="w-full text-left text-pink-400 hover:text-pink-300 font-medium transition-colors"
                  >
                    {showAdvanced ? 'Hide' : 'Show'} Advanced Options
                  </button>

                  {showAdvanced && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="space-y-4"
                    >
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Target Keyword
                        </label>
                        <input
                          type="text"
                          value={formData.targetKeyword}
                          onChange={(e) => setFormData({...formData, targetKeyword: e.target.value})}
                          placeholder="Primary SEO keyword"
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Target Audience
                        </label>
                        <input
                          type="text"
                          value={formData.targetAudience}
                          onChange={(e) => setFormData({...formData, targetAudience: e.target.value})}
                          placeholder="Who is this content for?"
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"
                        />
                      </div>
                    </motion.div>
                  )}

                  {/* Analyze Button */}
                  <motion.button
                    type="submit"
                    disabled={isAnalyzing || !formData.topic}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full py-4 bg-gradient-to-r from-pink-600 to-rose-600 text-white font-semibold rounded-xl hover:from-pink-700 hover:to-rose-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2"
                  >
                    {isAnalyzing ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Creating Content...</span>
                      </>
                    ) : (
                      <>
                        <Zap className="w-5 h-5" />
                        <span>Generate Article</span>
                      </>
                    )}
                  </motion.button>
                </form>
              </div>
            </motion.div>

            {/* Multi-Step Output Panel */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <AnimatePresence mode="wait">
                {/* Form Step */}
                {currentStep === 'form' && (
                  <motion.div
                    key="form-panel"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
                  >
                    <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                      <Target className="w-5 h-5 mr-2 text-pink-400" />
                      Ready to Generate
                    </h2>
                    <div className="text-center py-12 text-gray-400">
                      <Zap className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p>Fill out the form and click "Generate Article" to begin</p>
                      <p className="text-sm mt-2">We'll research, analyze, and create your content automatically</p>
                    </div>
                  </motion.div>
                )}

                {/* Analyzing Step */}
                {currentStep === 'analyzing' && (
                  <motion.div
                    key="analyzing-panel"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
                  >
                    <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                      <Clock className="w-5 h-5 mr-2 text-pink-400 animate-spin" />
                      Analyzing Topic
                    </h2>
                    <div className="space-y-4">
                      {progressSteps.map((step, index) => (
                        <div key={index} className="flex items-center space-x-3">
                          {index < currentProgressStep ? (
                            <CheckCircle className="w-5 h-5 text-green-400" />
                          ) : index === currentProgressStep ? (
                            <div className="w-5 h-5 border-2 border-pink-400 border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <div className="w-5 h-5 border-2 border-gray-600 rounded-full" />
                          )}
                          <span className={cn(
                            "text-sm",
                            index <= currentProgressStep ? "text-white" : "text-gray-500"
                          )}>
                            {step}
                          </span>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                )}

                {/* Generating Step */}
                {currentStep === 'generating' && (
                  <motion.div
                    key="generating-panel"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
                  >
                    <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                      <Zap className="w-5 h-5 mr-2 text-yellow-400 animate-pulse" />
                      Writing Your Article
                    </h2>
                    <div className="text-center py-12 text-gray-400">
                      <div className="animate-spin w-12 h-12 border-2 border-pink-400 border-t-transparent rounded-full mx-auto mb-4"></div>
                      <p>Creating your SEO-optimized article...</p>
                      <p className="text-sm mt-2">Using research insights and competitive analysis</p>
                    </div>
                  </motion.div>
                )}

                {/* Complete Step - Enhanced Article View */}
                {currentStep === 'complete' && generatedContent && (
                  <motion.div
                    key="complete-panel"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="space-y-6"
                  >
                    {/* Article Header with Metadata */}
                    <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h2 className="text-xl font-semibold text-white flex items-center">
                          <CheckCircle className="w-5 h-5 mr-2 text-green-400" />
                          Article Generated Successfully
                        </h2>
                        <div className="flex space-x-2">
                          <button
                            onClick={copyToClipboard}
                            className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors tooltip"
                            title="Copy to Clipboard"
                          >
                            <Copy className="w-4 h-4 text-white" />
                          </button>
                          <button
                            onClick={downloadAsMarkdown}
                            className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors tooltip"
                            title="Download as Markdown"
                          >
                            <Download className="w-4 h-4 text-white" />
                          </button>
                          <button
                            onClick={handleReset}
                            className="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg transition-colors text-sm"
                          >
                            New Article
                          </button>
                        </div>
                      </div>
                      
                      {/* Content Metadata */}
                      {contentMetadata && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <div className="bg-white/5 rounded-lg p-3 text-center">
                            <div className="text-2xl font-bold text-white">{contentMetadata.wordCount || 0}</div>
                            <div className="text-xs text-gray-400">Words</div>
                          </div>
                          <div className="bg-white/5 rounded-lg p-3 text-center">
                            <div className="text-2xl font-bold text-green-400">{contentMetadata.externalSourcesCount || 0}</div>
                            <div className="text-xs text-gray-400">Sources</div>
                          </div>
                          <div className="bg-white/5 rounded-lg p-3 text-center">
                            <div className="text-2xl font-bold text-blue-400">{contentMetadata.tokensUsed || 0}</div>
                            <div className="text-xs text-gray-400">Tokens</div>
                          </div>
                          <div className="bg-white/5 rounded-lg p-3 text-center">
                            <div className="text-2xl font-bold text-purple-400">
                              {contentMetadata.dataEnhanced ? '✓' : '○'}
                            </div>
                            <div className="text-xs text-gray-400">Data Enhanced</div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* External Sources Panel */}
                    {externalSources.length > 0 && (
                      <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                          <Search className="w-5 h-5 mr-2 text-blue-400" />
                          Research Sources Used ({externalSources.length})
                        </h3>
                        <div className="grid gap-3">
                          {externalSources.map((source, index) => (
                            <div key={index} className="bg-white/5 rounded-lg p-4 border border-white/10">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h4 className="font-semibold text-white text-sm mb-1">{source.title}</h4>
                                  <a 
                                    href={source.url} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="text-blue-400 hover:text-blue-300 text-xs break-all transition-colors"
                                  >
                                    {source.url}
                                  </a>
                                  {source.strengths && source.strengths.length > 0 && (
                                    <div className="mt-2">
                                      <span className="text-xs text-gray-400">Key Strengths: </span>
                                      <span className="text-xs text-gray-300">{source.strengths.join(', ')}</span>
                                    </div>
                                  )}
                                </div>
                                <div className="flex items-center space-x-3 ml-4">
                                  <div className="text-center">
                                    <div className="text-sm font-bold text-green-400">{source.qualityScore}/100</div>
                                    <div className="text-xs text-gray-400">Quality</div>
                                  </div>
                                  <div className="text-center">
                                    <div className="text-sm font-bold text-white">{source.wordCount}</div>
                                    <div className="text-xs text-gray-400">Words</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Enhanced Article Display */}
                    <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden">
                      <div className="bg-gradient-to-r from-pink-600/20 to-rose-600/20 px-6 py-4 border-b border-white/10">
                        <h3 className="text-lg font-semibold text-white flex items-center">
                          <FileText className="w-5 h-5 mr-2" />
                          Generated Article
                        </h3>
                      </div>
                      <div className="p-6">
                        <div className="prose prose-invert prose-pink max-w-none">
                          <div 
                            className="text-gray-100 leading-relaxed"
                            style={{ 
                              whiteSpace: 'pre-wrap',
                              fontFamily: 'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif',
                              fontSize: '16px',
                              lineHeight: '1.7'
                            }}
                          >
                            {generatedContent.split('\n').map((paragraph, index) => {
                              // Handle different markdown elements
                              if (paragraph.startsWith('# ')) {
                                return (
                                  <h1 key={index} className="text-3xl font-bold text-white mt-8 mb-4 first:mt-0">
                                    {paragraph.replace('# ', '')}
                                  </h1>
                                )
                              } else if (paragraph.startsWith('## ')) {
                                return (
                                  <h2 key={index} className="text-2xl font-semibold text-white mt-6 mb-3">
                                    {paragraph.replace('## ', '')}
                                  </h2>
                                )
                              } else if (paragraph.startsWith('### ')) {
                                return (
                                  <h3 key={index} className="text-xl font-semibold text-white mt-5 mb-3">
                                    {paragraph.replace('### ', '')}
                                  </h3>
                                )
                              } else if (paragraph.startsWith('- ') || paragraph.startsWith('* ')) {
                                return (
                                  <li key={index} className="text-gray-200 ml-4 mb-1">
                                    {paragraph.replace(/^[*-] /, '')}
                                  </li>
                                )
                              } else if (paragraph.match(/^\d+\. /)) {
                                return (
                                  <li key={index} className="text-gray-200 ml-4 mb-1 list-decimal">
                                    {paragraph.replace(/^\d+\. /, '')}
                                  </li>
                                )
                              } else if (paragraph.includes('[') && paragraph.includes('](')) {
                                // Handle links
                                const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g
                                const parts = paragraph.split(linkRegex)
                                return (
                                  <p key={index} className="text-gray-200 mb-4">
                                    {parts.map((part, i) => {
                                      if (i % 3 === 1) {
                                        // This is link text
                                        return (
                                          <a
                                            key={i}
                                            href={parts[i + 1]}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-400 hover:text-blue-300 underline transition-colors"
                                          >
                                            {part}
                                          </a>
                                        )
                                      } else if (i % 3 === 2) {
                                        // This is the URL (skip it as it's already used)
                                        return null
                                      } else {
                                        // Regular text
                                        return part
                                      }
                                    })}
                                  </p>
                                )
                              } else if (paragraph.trim() === '') {
                                return <div key={index} className="h-4" />
                              } else {
                                return (
                                  <p key={index} className="text-gray-200 mb-4">
                                    {paragraph}
                                  </p>
                                )
                              }
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
} 