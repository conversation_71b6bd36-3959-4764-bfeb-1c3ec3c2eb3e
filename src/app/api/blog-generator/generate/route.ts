import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { QuotaManager } from '@/lib/quota';
import { GeminiService } from '@/lib/gemini';
import { KnowledgeBaseService } from '@/lib/services/knowledge-base-service';
import { prisma } from '@/lib/prisma';

/**
 * Enhanced Blog Generator Content Generation Endpoint
 * Phase 2: Generate content using knowledge base and competitive analysis
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    const userId = session.user.id;

    const { 
      topic,
      targetKeyword,
      wordCount = 2000,
      tone = 'professional',
      title,
      targetAudience,
      analysisId,
      useKnowledgeBase = true,
      useCompetitiveAnalysis = true,
      analysisData // Contains the research data from the analysis phase
    } = await request.json();

    if (!topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      );
    }

    console.log(`✍️ Starting enhanced content generation for: "${topic}"`);
    
    let researchData = '';
    let competitiveInsights = '';
    let seoRecommendations = '';
    let externalSources = [];
    
    // Phase 1: Extract Research Data and External Sources
    if (analysisData) {
      console.log('📚 Phase 1: Processing analysis data for content generation...');
      
      // Extract competitive analysis insights
      if (analysisData.competitiveAnalysis) {
        const compAnalysis = analysisData.competitiveAnalysis;
        
        competitiveInsights = `
COMPETITIVE LANDSCAPE INSIGHTS:
===============================

📊 Market Analysis:
• ${compAnalysis.competitorsAnalyzed} competitors analyzed
• Average competitor quality score: ${Math.round(compAnalysis.averageCompetitorScore)}/100
• Recommended word count: ${compAnalysis.recommendedWordCount} words

🔍 Common Keywords Found:
${compAnalysis.commonKeywords.map(kw => `• ${kw}`).join('\n')}

📈 Content Opportunities:
${compAnalysis.contentGaps.map(gap => `• ${gap}`).join('\n')}

🚀 Strategic Advantages:
${compAnalysis.opportunities.map(opp => `• ${opp}`).join('\n')}

📋 Content Strategy:
${compAnalysis.contentStrategy.contentDepth}
Target Audience: ${compAnalysis.contentStrategy.targetAudience}
`;
      }

      // Extract top competitor data and external sources
      if (analysisData.topCompetitors) {
        externalSources = analysisData.topCompetitors.map(comp => ({
          url: comp.url,
          title: comp.title,
          score: comp.overallScore,
          wordCount: comp.wordCount,
          strengths: comp.strengths,
        }));
        
        researchData = `
RESEARCH DATA FROM TOP SOURCES:
==============================

${analysisData.topCompetitors.map((comp, index) => `
SOURCE ${index + 1}: ${comp.title}
URL: ${comp.url}
Quality Score: ${comp.overallScore}/100
Word Count: ${comp.wordCount} words
Key Strengths: ${comp.strengths.join(', ')}
Content Gaps: ${comp.gaps.join(', ')}
`).join('\n')}

SEARCH INSIGHTS:
===============
• Total sources analyzed: ${analysisData.searchSummary.totalResults}
• Successful extractions: ${analysisData.searchSummary.successfulExtractions}
• Search query: "${analysisData.searchSummary.searchQuery}"
`;
      }

      // Extract SEO insights
      if (analysisData.seoInsights) {
        const seoInsights = analysisData.seoInsights;
        
        seoRecommendations = `
SEO OPTIMIZATION STRATEGY:
=========================

🎯 Target Keyword Opportunity:
${seoInsights.targetKeywordOpportunity ? `
• Competitors using keyword: ${seoInsights.targetKeywordOpportunity.competitorsUsingKeyword}/${analysisData.competitiveAnalysis.competitorsAnalyzed}
• Average title length: ${Math.round(seoInsights.targetKeywordOpportunity.averageTitleLength)} characters
• Average meta description: ${Math.round(seoInsights.targetKeywordOpportunity.averageMetaLength)} characters
` : 'Focus on natural keyword integration'}

📝 Content Structure Recommendations:
${seoInsights.commonHeadingPatterns.map(pattern => `• ${pattern}`).join('\n')}

🎯 Recommended Structure:
${seoInsights.recommendedStructure.map(structure => `• ${structure}`).join('\n')}
`;
      }
      
      console.log(`✅ Processed analysis data: ${externalSources.length} sources, ${competitiveInsights.length} chars insights`);
    }
    
    // Phase 3: Generate Enhanced Content with Gemini
    console.log('🤖 Phase 3: Generating content with Gemini 2.5 Flash Lite...');
    
    const gemini = new GeminiService();
    
    const enhancedPrompt = `
You are a world-class SEO content strategist and expert data-driven writer. Create a comprehensive, authoritative, and information-rich blog post packed with data, statistics, and credible external references.

CONTENT REQUIREMENTS:
====================
Topic: ${topic}
Target Keyword: ${targetKeyword || 'Focus on topic naturally'}
Word Count: ${wordCount} words (minimum)
Tone: ${tone}
Target Audience: ${targetAudience || 'General audience interested in the topic'}
${title ? `Custom Title: ${title}` : 'Generate an optimal SEO title'}

RESEARCH-BASED COMPETITIVE ANALYSIS:
====================================
${competitiveInsights}

SEO OPTIMIZATION STRATEGY:
==========================
${seoRecommendations}

COMPREHENSIVE RESEARCH DATA:
===========================
${researchData}

EXTERNAL SOURCES TO REFERENCE:
==============================
${externalSources.map((source, index) => `
${index + 1}. ${source.title}
   URL: ${source.url}
   Quality Score: ${source.score}/100
   Word Count: ${source.wordCount} words
   Key Strengths: ${source.strengths?.join(', ') || 'Comprehensive coverage'}
`).join('\n')}

CONTENT GENERATION INSTRUCTIONS:
===============================

1. TITLE CREATION:
   - Create a data-driven, compelling title (50-60 characters)
   - Include target keyword naturally and add quantifiable elements (e.g., "2025 Guide", "Top 10", "Complete Analysis")
   - Use power words like "Ultimate", "Complete", "Data-Driven", "Comprehensive"
   - Make it click-worthy and authoritative

2. INFORMATION-RICH CONTENT STRUCTURE:
   - Start with a compelling hook using a relevant statistic or data point
   - Write a comprehensive introduction with key statistics and what readers will learn
   - Use clear H2 and H3 headings that include variations of the target keyword
   - Create scannable content with data tables, comparison charts, and infographic-style sections
   - Include extensive practical examples, case studies, and real-world applications
   - Add a strong conclusion with actionable next steps and key takeaways

3. DATA-DRIVEN CONTENT REQUIREMENTS:
   - Include relevant statistics, percentages, and quantifiable data throughout
   - Reference specific studies, surveys, and industry reports
   - Provide market analysis with concrete numbers (user bases, adoption rates, pricing)
   - Include performance benchmarks, comparison metrics, and measurable outcomes
   - Use data to support every major claim and recommendation
   - Create data visualizations through descriptive text (tables, charts, comparisons)

4. EXTERNAL LINKS AND CREDIBILITY:
   - Reference and link to the provided external sources strategically
   - Format links as: [Link text](URL) - Brief description
   - Include authoritative sources to back up statistics and claims
   - Reference industry leaders, official documentation, and credible publications
   - Ensure each major section has 1-2 relevant external references
   - Balance between external credibility and content flow

5. COMPREHENSIVE COMPETITIVE INTELLIGENCE:
   - Integrate insights from the competitive analysis throughout
   - Address all identified content gaps with detailed explanations
   - Provide unique perspectives that differentiate from competitors
   - Include market positioning and comparative analysis
   - Offer strategic insights based on competitive landscape data

6. ENHANCED SEO AND ENGAGEMENT:
   - Use target keyword naturally throughout (1-2% density)
   - Include semantic keywords and industry-specific terminology
   - Create content that thoroughly answers user intent with data backing
   - Include internal linking opportunities (mention as [INTERNAL LINK: anchor text])
   - Write in active voice with industry expertise and data authority
   - Include multiple calls-to-action with specific, measurable outcomes

CONTENT FORMAT:
==============
- Use proper markdown formatting
- Include a compelling title
- Structure with clear headings and subheadings
- Add bullet points and numbered lists for key information
- Include tables for comparisons where appropriate
- End with a strong call-to-action

Your goal is to create the definitive resource on this topic that will naturally outrank all competitors through superior content quality, structure, and optimization.
`;

    const result = await gemini.generateContent(enhancedPrompt, {
      temperature: 0.7,
      maxOutputTokens: 8000,
      thinkingConfig: {
        thinkingBudget: -1, // Dynamic thinking
        includeThoughts: false
      }
    });

    const generatedContent = result.response;
    
    console.log(`✅ Content generated: ${generatedContent.length} characters`);
    
    // Phase 4: Update quota usage
    let updatedQuota;
    try {
      updatedQuota = await QuotaManager.useQuota(userId, 'blog_posts');
      console.log(`📊 Quota updated: ${updatedQuota.used}/${updatedQuota.limit} used`);
    } catch (quotaError) {
      console.warn('Failed to update quota:', quotaError);
      updatedQuota = { used: 1, limit: 100 }; // Fallback
    }
    
    // Phase 5: Save generated content (optional - for future enhancements)
    console.log('⏭️ Content generation complete, skipping storage for now');
    
    console.log('✅ Enhanced blog generation complete!');
    
    return NextResponse.json({
      success: true,
      content: generatedContent,
      metadata: {
        wordCount: generatedContent.split(/\s+/).length,
        tokensUsed: result.outputTokens,
        researchDataUsed: researchData.length > 0,
        competitiveAnalysisUsed: competitiveInsights.length > 0,
        seoOptimized: true,
        generationModel: 'gemini-2.5-flash-lite',
        externalSourcesCount: externalSources.length,
        dataEnhanced: true,
      },
      externalSources: externalSources.map(source => ({
        title: source.title,
        url: source.url,
        qualityScore: source.score,
        wordCount: source.wordCount,
        strengths: source.strengths || []
      })),
      quota: {
        used: updatedQuota.used,
        limit: updatedQuota.limit,
        remaining: updatedQuota.limit === -1 ? -1 : updatedQuota.limit - updatedQuota.used
      }
    });

  } catch (error) {
    console.error('Enhanced blog generation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate enhanced blog content',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}