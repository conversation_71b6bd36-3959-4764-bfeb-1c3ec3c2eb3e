'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import BlogStyleDisplay from '@/components/BlogStyleDisplay'

export default function BlogDisplayByIdPage() {
  const params = useParams()
  const [content, setContent] = useState('')
  const [title, setTitle] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchContent = async () => {
      try {
        const response = await fetch(`/api/blog-content/${params.id}`)
        if (!response.ok) {
          throw new Error('Content not found')
        }
        const data = await response.json()
        setContent(data.content)
        setTitle(data.title)
      } catch (error) {
        console.error('Error fetching content:', error)
        setError('Content not found or expired')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchContent()
    }
  }, [params.id])

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
  }

  if (loading) {
    return (
      <div className="min-h-screen warm-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="warm-text">Loading your blog post...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen warm-bg flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😕</div>
          <h1 className="text-2xl font-bold warm-heading mb-2">Content Not Found</h1>
          <p className="warm-text mb-6">{error}</p>
          <Link href="/blog-generator">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors"
            >
              Generate New Blog Post
            </motion.button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      {/* Back Button */}
      <div className="absolute top-4 left-4 z-20">
        <Link href="/blog-generator">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-3 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded-lg transition-colors shadow-lg"
          >
            <ArrowLeft className="w-5 h-5 text-orange-700" />
          </motion.button>
        </Link>
      </div>

      {/* Blog Display Component */}
      <BlogStyleDisplay
        content={content}
        title={title}
        onContentChange={handleContentChange}
        editable={true}
      />
    </div>
  )
}
