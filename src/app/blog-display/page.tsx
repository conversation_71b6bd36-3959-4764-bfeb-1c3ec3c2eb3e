'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import BlogStyleDisplay from '@/components/BlogStyleDisplay'

export default function BlogDisplayPage() {
  const searchParams = useSearchParams()
  const [content, setContent] = useState('')
  const [title, setTitle] = useState('')

  useEffect(() => {
    const contentParam = searchParams.get('content')
    const titleParam = searchParams.get('title')

    if (contentParam) {
      const decodedContent = decodeURIComponent(contentParam)
      setContent(decodedContent)
    }

    if (titleParam) {
      setTitle(decodeURIComponent(titleParam))
    }
  }, [searchParams])

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
  }

  if (!content) {
    return (
      <div className="min-h-screen warm-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="warm-text">Loading your blog post...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      {/* Back Button */}
      <div className="absolute top-4 left-4 z-20">
        <Link href="/simple-blog-generator">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-3 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded-lg transition-colors shadow-lg"
          >
            <ArrowLeft className="w-5 h-5 text-orange-700" />
          </motion.button>
        </Link>
      </div>

      {/* Blog Display Component */}
      <BlogStyleDisplay
        content={content}
        title={title}
        onContentChange={handleContentChange}
        editable={true}
      />
    </div>
  )
}
