'use client'

import { useState, useEffect } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { motion } from 'framer-motion'
import { Copy, Download, Edit, Eye, CheckCircle, Save, X } from 'lucide-react'

interface BlogStyleDisplayProps {
  content: string
  title?: string
  onContentChange?: (content: string) => void
  editable?: boolean
  className?: string
}

export default function BlogStyleDisplay({ 
  content, 
  title, 
  onContentChange, 
  editable = true,
  className = ""
}: BlogStyleDisplayProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedContent, setEditedContent] = useState(content)
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    setEditedContent(content)
  }, [content])

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(isEditing ? editedContent : content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const handleDownload = () => {
    const contentToDownload = isEditing ? editedContent : content
    const blob = new Blob([contentToDownload], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title || 'blog-post'}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleSaveEdit = () => {
    if (onContentChange) {
      onContentChange(editedContent)
    }
    setIsEditing(false)
  }

  const handleCancelEdit = () => {
    setEditedContent(content)
    setIsEditing(false)
  }

  return (
    <div className={`warm-bg min-h-screen ${className}`}>
      {/* Toolbar */}
      <div className="bg-white/90 backdrop-blur-sm border-b border-amber-200 sticky top-0 z-10 p-4">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <div>
            {title && <h1 className="text-xl font-bold warm-heading">{title}</h1>}
            <p className="text-sm warm-accent">
              {isEditing ? 'Editing Mode' : 'Preview Mode'}
            </p>
          </div>

          <div className="flex items-center space-x-2">
            {editable && (
              <>
                {isEditing ? (
                  <>
                    <motion.button
                      onClick={handleSaveEdit}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors text-sm"
                    >
                      <Save className="w-4 h-4" />
                      <span>Save</span>
                    </motion.button>
                    <motion.button
                      onClick={handleCancelEdit}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center space-x-2 px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors text-sm"
                    >
                      <X className="w-4 h-4" />
                      <span>Cancel</span>
                    </motion.button>
                  </>
                ) : (
                  <motion.button
                    onClick={() => setIsEditing(true)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded-lg transition-colors"
                    title="Edit Content"
                  >
                    <Edit className="w-4 h-4 text-orange-700" />
                  </motion.button>
                )}
              </>
            )}

            <motion.button
              onClick={handleCopy}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded-lg transition-colors"
              title="Copy to Clipboard"
            >
              {copied ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <Copy className="w-4 h-4 text-orange-700" />
              )}
            </motion.button>

            <motion.button
              onClick={handleDownload}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded-lg transition-colors"
              title="Download as Markdown"
            >
              <Download className="w-4 h-4 text-orange-700" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-4xl mx-auto"
        >
          {isEditing ? (
            <div className="warm-card p-6 shadow-xl">
              <div className="mb-4">
                <h2 className="text-lg font-semibold warm-heading mb-2">Edit Content</h2>
                <p className="text-sm warm-text opacity-75">
                  Edit your content in Markdown format. Changes will be reflected in the preview.
                </p>
              </div>
              <textarea
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                className="w-full h-96 p-4 border border-amber-300 rounded-lg warm-text bg-white/70 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all resize-none font-mono text-sm leading-relaxed"
                placeholder="Edit your blog post content in Markdown..."
              />
              <div className="mt-4 text-xs warm-text opacity-60">
                <p>Tip: Use Markdown syntax for formatting (# for headings, ** for bold, * for italic, etc.)</p>
              </div>
            </div>
          ) : (
            <div className="warm-card p-8 shadow-xl">
              <div className="prose prose-lg max-w-none">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    h1: ({ children }) => (
                      <h1 className="text-4xl font-bold warm-heading mb-8 leading-tight border-b-2 border-amber-300 pb-4">
                        {children}
                      </h1>
                    ),
                    h2: ({ children }) => (
                      <h2 className="text-3xl font-bold warm-heading mb-6 mt-12 leading-tight pl-4 border-l-4 border-orange-400">
                        {children}
                      </h2>
                    ),
                    h3: ({ children }) => (
                      <h3 className="text-2xl font-semibold warm-heading mb-4 mt-8">
                        {children}
                      </h3>
                    ),
                    h4: ({ children }) => (
                      <h4 className="text-xl font-semibold warm-heading mb-3 mt-6">
                        {children}
                      </h4>
                    ),
                    p: ({ children }) => (
                      <p className="warm-text leading-relaxed mb-6 text-lg">
                        {children}
                      </p>
                    ),
                    ul: ({ children }) => (
                      <ul className="space-y-3 mb-8 ml-6 list-disc list-outside">
                        {children}
                      </ul>
                    ),
                    ol: ({ children }) => (
                      <ol className="space-y-3 mb-8 ml-6 list-decimal list-outside">
                        {children}
                      </ol>
                    ),
                    li: ({ children }) => (
                      <li className="warm-text text-lg leading-relaxed hover:text-orange-800 transition-colors">
                        {children}
                      </li>
                    ),
                    strong: ({ children }) => (
                      <strong className="font-bold text-orange-800 bg-gradient-to-r from-yellow-200 to-amber-200 px-2 py-1 rounded-lg shadow-sm">
                        {children}
                      </strong>
                    ),
                    em: ({ children }) => (
                      <em className="italic warm-accent font-medium">{children}</em>
                    ),
                    blockquote: ({ children }) => (
                      <blockquote className="border-l-4 border-orange-400 pl-8 pr-4 italic warm-text my-8 bg-gradient-to-r from-orange-100 to-yellow-100 py-6 rounded-r-xl shadow-lg relative">
                        <div className="text-xl leading-relaxed">
                          <span className="absolute -top-2 -left-2 text-3xl text-orange-400 opacity-50">"</span>
                          {children}
                          <span className="absolute -bottom-4 -right-2 text-3xl text-orange-400 opacity-50">"</span>
                        </div>
                      </blockquote>
                    ),
                    code: ({ children }) => (
                      <code className="bg-amber-100 text-orange-800 px-3 py-1 rounded-lg text-base font-mono border border-amber-300 shadow-sm">
                        {children}
                      </code>
                    ),
                    pre: ({ children }) => (
                      <pre className="bg-gradient-to-br from-orange-900 to-red-900 text-orange-100 p-6 rounded-xl overflow-x-auto my-6 border border-orange-700 shadow-xl">
                        <code className="text-sm leading-relaxed">{children}</code>
                      </pre>
                    ),
                    table: ({ children }) => (
                      <div className="overflow-x-auto my-8 rounded-lg shadow-lg">
                        <table className="min-w-full border-collapse border border-amber-300 rounded-lg overflow-hidden">
                          {children}
                        </table>
                      </div>
                    ),
                    th: ({ children }) => (
                      <th className="border border-amber-300 px-6 py-4 bg-gradient-to-r from-orange-200 to-amber-200 warm-heading font-semibold text-left">
                        {children}
                      </th>
                    ),
                    td: ({ children }) => (
                      <td className="border border-amber-300 px-6 py-4 warm-text bg-white/70 hover:bg-white/90 transition-colors">
                        {children}
                      </td>
                    ),
                    a: ({ children, href }) => (
                      <a 
                        href={href} 
                        className="warm-accent hover:text-orange-700 underline decoration-orange-400 hover:decoration-orange-600 transition-all duration-200 font-medium"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {children}
                      </a>
                    ),
                    hr: () => (
                      <hr className="my-8 border-0 h-px bg-gradient-to-r from-transparent via-amber-300 to-transparent" />
                    )
                  }}
                >
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}
