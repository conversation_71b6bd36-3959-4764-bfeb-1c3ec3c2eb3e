// Simple test script for the blog generator API
// Run with: node test-simple-blog.js

const testBlogGeneration = async () => {
  try {
    console.log('🚀 Testing Simple Blog Generator API...')
    
    const response = await fetch('http://localhost:3000/api/simple-blog/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: This test won't work without proper authentication
        // It's just to verify the endpoint structure
      },
      body: JSON.stringify({
        topic: 'The Future of AI in Content Creation',
        wordCount: 1500,
        tone: 'professional'
      })
    })

    console.log('📡 Response status:', response.status)
    console.log('📋 Response headers:', Object.fromEntries(response.headers.entries()))

    if (response.status === 401) {
      console.log('✅ Authentication check working correctly (401 expected without session)')
      return
    }

    if (response.ok) {
      console.log('📖 Reading streaming response...')
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          console.log('📦 Received chunk:', chunk)
        }
      }
    } else {
      const errorText = await response.text()
      console.log('❌ Error response:', errorText)
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message)
  }
}

// Run the test
testBlogGeneration()
