"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog-generator/page",{

/***/ "(app-pages-browser)/./src/app/blog-generator/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/blog-generator/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogGeneratorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,CheckCircle,Clock,Copy,Download,FileText,Search,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction BlogGeneratorPage() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        topic: '',\n        title: '',\n        wordCount: 2000,\n        tone: 'professional',\n        targetKeyword: '',\n        targetAudience: '',\n        numResults: 10\n    });\n    // Multi-step states\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('form') // 'form', 'analyzing', 'generating', 'complete'\n    ;\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedContent, setGeneratedContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [externalSources, setExternalSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [contentMetadata, setContentMetadata] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showAdvanced, setShowAdvanced] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progressSteps, setProgressSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentProgressStep, setCurrentProgressStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BlogGeneratorPage.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"BlogGeneratorPage.useEffect\"], [\n        status,\n        router\n    ]);\n    // Enhanced progress tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BlogGeneratorPage.useEffect\": ()=>{\n            let interval;\n            if (isAnalyzing && currentProgressStep < progressSteps.length - 1) {\n                // Variable timing based on actual API phases\n                const timings = [\n                    4000,\n                    8000,\n                    3000,\n                    6000,\n                    2000\n                ] // Realistic timings for each phase\n                ;\n                const currentTiming = timings[currentProgressStep] || 3000;\n                interval = setTimeout({\n                    \"BlogGeneratorPage.useEffect\": ()=>{\n                        setCurrentProgressStep({\n                            \"BlogGeneratorPage.useEffect\": (prev)=>Math.min(prev + 1, progressSteps.length - 1)\n                        }[\"BlogGeneratorPage.useEffect\"]);\n                    }\n                }[\"BlogGeneratorPage.useEffect\"], currentTiming);\n            }\n            return ({\n                \"BlogGeneratorPage.useEffect\": ()=>clearTimeout(interval)\n            })[\"BlogGeneratorPage.useEffect\"];\n        }\n    }[\"BlogGeneratorPage.useEffect\"], [\n        isAnalyzing,\n        currentProgressStep,\n        progressSteps.length\n    ]);\n    // Progress tracking with estimated completion\n    const getProgressPercentage = ()=>{\n        if (currentStep === 'form') return 0;\n        if (currentStep === 'analyzing') return Math.min(currentProgressStep / progressSteps.length * 70, 65);\n        if (currentStep === 'generating') return 85;\n        if (currentStep === 'complete') return 100;\n        return 0;\n    };\n    const getEstimatedTime = ()=>{\n        if (currentStep === 'analyzing') return '3-4 minutes';\n        if (currentStep === 'generating') return '1-2 minutes';\n        return null;\n    };\n    // Loading state\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin w-12 h-12 border-2 border-pink-400 border-t-transparent rounded-full mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    // Start analysis phase\n    const handleAnalyze = async (e)=>{\n        e.preventDefault();\n        if (!formData.topic) return;\n        setIsAnalyzing(true);\n        setCurrentStep('analyzing');\n        setProgressSteps([\n            'Conducting Tavily search...',\n            'Extracting content from top pages...',\n            'Storing in knowledge base...',\n            'Analyzing competitors...',\n            'Generating insights...',\n            'Creating optimized content...'\n        ]);\n        setCurrentProgressStep(0);\n        try {\n            // Step 1: Run analysis\n            const analysisResponse = await fetch('/api/blog-generator/analyze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    topic: formData.topic,\n                    targetKeyword: formData.targetKeyword,\n                    numResults: formData.numResults\n                })\n            });\n            const analysisData = await analysisResponse.json();\n            if (!analysisData.success) {\n                alert('Analysis failed: ' + analysisData.error);\n                setCurrentStep('form');\n                setIsAnalyzing(false);\n                return;\n            }\n            // Step 2: Immediately start content generation\n            setCurrentStep('generating');\n            setIsGenerating(true);\n            const generationResponse = await fetch('/api/blog-generator/generate', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    topic: formData.topic,\n                    targetKeyword: formData.targetKeyword,\n                    wordCount: formData.wordCount,\n                    tone: formData.tone,\n                    title: formData.title,\n                    targetAudience: formData.targetAudience,\n                    analysisId: analysisData.metadata.analysisId,\n                    analysisData: analysisData.data,\n                    useKnowledgeBase: true,\n                    useCompetitiveAnalysis: true\n                })\n            });\n            const generationData = await generationResponse.json();\n            if (generationData.success) {\n                setGeneratedContent(generationData.content);\n                setExternalSources(generationData.externalSources || []);\n                setContentMetadata(generationData.metadata || {});\n                setCurrentStep('complete');\n            } else {\n                alert('Generation failed: ' + generationData.error);\n                setCurrentStep('form');\n            }\n        } catch (error) {\n            alert('Failed to generate content');\n            setCurrentStep('form');\n        } finally{\n            setIsAnalyzing(false);\n            setIsGenerating(false);\n        }\n    };\n    // Reset to form\n    const handleReset = ()=>{\n        setCurrentStep('form');\n        setGeneratedContent('');\n        setExternalSources([]);\n        setContentMetadata(null);\n        setFormData({\n            topic: '',\n            title: '',\n            wordCount: 2000,\n            tone: 'professional',\n            targetKeyword: '',\n            targetAudience: '',\n            numResults: 10\n        });\n    };\n    const copyToClipboard = ()=>{\n        navigator.clipboard.writeText(generatedContent);\n    };\n    const downloadAsMarkdown = ()=>{\n        const blob = new Blob([\n            generatedContent\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"\".concat(formData.topic.replace(/\\s+/g, '-').toLowerCase(), \".md\");\n        a.click();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-pink-900/20 via-black to-rose-900/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                -100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-pink-500/10 rounded-full blur-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            x: [\n                                0,\n                                -100,\n                                0\n                            ],\n                            y: [\n                                0,\n                                100,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 15,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-rose-500/10 rounded-full blur-[120px]\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-white/10 bg-black/60 backdrop-blur-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/dashboard\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.05\n                                                        },\n                                                        className: \"p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 rounded-2xl bg-gradient-to-br from-pink-600 to-rose-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-2xl font-bold text-white\",\n                                                                    children: \"Enhanced Blog Generator\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"AI-Powered SEO Content Creation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                getEstimatedTime() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-pink-400\",\n                                                                        children: [\n                                                                            \"Estimated time: \",\n                                                                            getEstimatedTime()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        currentStep !== 'form' && currentStep !== 'complete' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-white font-medium\",\n                                                            children: [\n                                                                currentStep === 'analyzing' && 'Researching & Analyzing...',\n                                                                currentStep === 'generating' && 'Writing Content...'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                Math.round(getProgressPercentage()),\n                                                                \"% complete\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 transform -rotate-90\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"32\",\n                                                                    cy: \"32\",\n                                                                    r: \"28\",\n                                                                    stroke: \"rgb(75, 85, 99)\",\n                                                                    strokeWidth: \"4\",\n                                                                    fill: \"none\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"32\",\n                                                                    cy: \"32\",\n                                                                    r: \"28\",\n                                                                    stroke: \"rgb(236, 72, 153)\",\n                                                                    strokeWidth: \"4\",\n                                                                    fill: \"none\",\n                                                                    strokeDasharray: \"\".concat(2 * Math.PI * 28),\n                                                                    strokeDashoffset: \"\".concat(2 * Math.PI * 28 * (1 - getProgressPercentage() / 100)),\n                                                                    className: \"transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-bold text-white\",\n                                                                children: [\n                                                                    Math.round(getProgressPercentage()),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            getProgressPercentage() > 0 && getProgressPercentage() < 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-1 bg-gray-800\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    className: \"h-full bg-gradient-to-r from-pink-500 to-rose-500\",\n                                    initial: {\n                                        width: 0\n                                    },\n                                    animate: {\n                                        width: \"\".concat(getProgressPercentage(), \"%\")\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-white mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2 text-pink-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Content Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleAnalyze,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Blog Topic *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.topic,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        topic: e.target.value\n                                                                    }),\n                                                                placeholder: \"Enter your blog topic...\",\n                                                                required: true,\n                                                                className: \"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Custom Title (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.title,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        title: e.target.value\n                                                                    }),\n                                                                placeholder: \"Leave blank for AI-generated title\",\n                                                                className: \"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Word Count\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: formData.wordCount,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                wordCount: parseInt(e.target.value)\n                                                                            }),\n                                                                        className: \"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 500,\n                                                                                children: \"500 words\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 370,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 1000,\n                                                                                children: \"1,000 words\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 371,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 1500,\n                                                                                children: \"1,500 words\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 2000,\n                                                                                children: \"2,000 words\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 3000,\n                                                                                children: \"3,000 words\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 374,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Tone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: formData.tone,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                tone: e.target.value\n                                                                            }),\n                                                                        className: \"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"professional\",\n                                                                                children: \"Professional\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 386,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"casual\",\n                                                                                children: \"Casual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"conversational\",\n                                                                                children: \"Conversational\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 388,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"authoritative\",\n                                                                                children: \"Authoritative\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 389,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"friendly\",\n                                                                                children: \"Friendly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 390,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"academic\",\n                                                                                children: \"Academic\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 391,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Research Depth\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.numResults,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        numResults: parseInt(e.target.value)\n                                                                    }),\n                                                                className: \"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: 5,\n                                                                        children: \"5 sources (Fast)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: 10,\n                                                                        children: \"10 sources (Recommended)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: 15,\n                                                                        children: \"15 sources (Comprehensive)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowAdvanced(!showAdvanced),\n                                                        className: \"w-full text-left text-pink-400 hover:text-pink-300 font-medium transition-colors\",\n                                                        children: [\n                                                            showAdvanced ? 'Hide' : 'Show',\n                                                            \" Advanced Options\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    showAdvanced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: 'auto'\n                                                        },\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Target Keyword\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: formData.targetKeyword,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                targetKeyword: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Primary SEO keyword\",\n                                                                        className: \"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Target Audience\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: formData.targetAudience,\n                                                                        onChange: (e)=>setFormData({\n                                                                                ...formData,\n                                                                                targetAudience: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Who is this content for?\",\n                                                                        className: \"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                                        type: \"submit\",\n                                                        disabled: isAnalyzing || !formData.topic,\n                                                        whileHover: {\n                                                            scale: 1.02\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.98\n                                                        },\n                                                        className: \"w-full py-4 bg-gradient-to-r from-pink-600 to-rose-600 text-white font-semibold rounded-xl hover:from-pink-700 hover:to-rose-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2\",\n                                                        children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Creating Content...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Generate Article\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: [\n                                            currentStep === 'form' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                exit: {\n                                                    opacity: 0\n                                                },\n                                                className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-6 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-5 h-5 mr-2 text-pink-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Ready to Generate\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-16 h-16 mx-auto mb-4 opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: 'Fill out the form and click \"Generate Article\" to begin'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mt-2\",\n                                                                children: \"We'll research, analyze, and create your content automatically\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"form-panel\", true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentStep === 'analyzing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                exit: {\n                                                    opacity: 0\n                                                },\n                                                className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-6 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 h-5 mr-2 text-pink-400 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Analyzing Topic\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: progressSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    index < currentProgressStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"w-5 h-5 text-green-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 29\n                                                                    }, this) : index === currentProgressStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-5 h-5 border-2 border-pink-400 border-t-transparent rounded-full animate-spin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 525,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-5 h-5 border-2 border-gray-600 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-sm\", index <= currentProgressStep ? \"text-white\" : \"text-gray-500\"),\n                                                                        children: step\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"analyzing-panel\", true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentStep === 'generating' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                exit: {\n                                                    opacity: 0\n                                                },\n                                                className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-6 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-5 h-5 mr-2 text-yellow-400 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Writing Your Article\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin w-12 h-12 border-2 border-pink-400 border-t-transparent rounded-full mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Creating your SEO-optimized article...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mt-2\",\n                                                                children: \"Using research insights and market intelligence\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"generating-panel\", true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentStep === 'complete' && generatedContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                exit: {\n                                                    opacity: 0\n                                                },\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-xl font-semibold text-white flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"w-5 h-5 mr-2 text-green-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Article Generated Successfully\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: copyToClipboard,\n                                                                                className: \"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors tooltip\",\n                                                                                title: \"Copy to Clipboard\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 584,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 579,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: downloadAsMarkdown,\n                                                                                className: \"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors tooltip\",\n                                                                                title: \"Download as Markdown\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 591,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 586,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleReset,\n                                                                                className: \"px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg transition-colors text-sm\",\n                                                                                children: \"New Article\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            contentMetadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 rounded-lg p-3 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-2xl font-bold text-white\",\n                                                                                children: contentMetadata.wordCount || 0\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 606,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: \"Words\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 607,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 rounded-lg p-3 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-2xl font-bold text-green-400\",\n                                                                                children: contentMetadata.externalSourcesCount || 0\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 610,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: \"Sources\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 611,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 609,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 rounded-lg p-3 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-2xl font-bold text-blue-400\",\n                                                                                children: contentMetadata.tokensUsed || 0\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: \"Tokens\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 615,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 rounded-lg p-3 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-2xl font-bold text-purple-400\",\n                                                                                children: contentMetadata.dataEnhanced ? '✓' : '○'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 618,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: \"Data Enhanced\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    externalSources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white mb-4 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-5 h-5 mr-2 text-blue-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Research Sources Used (\",\n                                                                    externalSources.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid gap-3\",\n                                                                children: externalSources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 rounded-lg p-4 border border-white/10\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-semibold text-white text-sm mb-1\",\n                                                                                            children: source.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                            lineNumber: 639,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                            href: source.url,\n                                                                                            target: \"_blank\",\n                                                                                            rel: \"noopener noreferrer\",\n                                                                                            className: \"text-blue-400 hover:text-blue-300 text-xs break-all transition-colors\",\n                                                                                            children: source.url\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                            lineNumber: 640,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        source.strengths && source.strengths.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"mt-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs text-gray-400\",\n                                                                                                    children: \"Key Strengths: \"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                                    lineNumber: 650,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs text-gray-300\",\n                                                                                                    children: source.strengths.join(', ')\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                                    lineNumber: 651,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                            lineNumber: 649,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 638,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-3 ml-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm font-bold text-green-400\",\n                                                                                                    children: [\n                                                                                                        source.qualityScore,\n                                                                                                        \"/100\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                                    lineNumber: 657,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-400\",\n                                                                                                    children: \"Quality\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                                    lineNumber: 658,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                            lineNumber: 656,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm font-bold text-white\",\n                                                                                                    children: source.wordCount\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                                    lineNumber: 661,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-400\",\n                                                                                                    children: \"Words\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                                    lineNumber: 662,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                            lineNumber: 660,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 655,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, index, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 636,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-pink-600/20 to-rose-600/20 px-6 py-4 border-b border-white/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-white flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_CheckCircle_Clock_Copy_Download_FileText_Search_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"w-5 h-5 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                            lineNumber: 676,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Generated Article\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"prose prose-invert prose-pink max-w-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-100 leading-relaxed\",\n                                                                        style: {\n                                                                            whiteSpace: 'pre-wrap',\n                                                                            fontFamily: 'ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif',\n                                                                            fontSize: '16px',\n                                                                            lineHeight: '1.7'\n                                                                        },\n                                                                        children: generatedContent.split('\\n').map((paragraph, index)=>{\n                                                                            // Handle different markdown elements\n                                                                            if (paragraph.startsWith('# ')) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                                    className: \"text-3xl font-bold text-white mt-8 mb-4 first:mt-0\",\n                                                                                    children: paragraph.replace('# ', '')\n                                                                                }, index, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 695,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            } else if (paragraph.startsWith('## ')) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                                    className: \"text-2xl font-semibold text-white mt-6 mb-3\",\n                                                                                    children: paragraph.replace('## ', '')\n                                                                                }, index, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 701,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            } else if (paragraph.startsWith('### ')) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-xl font-semibold text-white mt-5 mb-3\",\n                                                                                    children: paragraph.replace('### ', '')\n                                                                                }, index, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 707,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            } else if (paragraph.startsWith('- ') || paragraph.startsWith('* ')) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"text-gray-200 ml-4 mb-1\",\n                                                                                    children: paragraph.replace(/^[*-] /, '')\n                                                                                }, index, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 713,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            } else if (paragraph.match(/^\\d+\\. /)) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    className: \"text-gray-200 ml-4 mb-1 list-decimal\",\n                                                                                    children: paragraph.replace(/^\\d+\\. /, '')\n                                                                                }, index, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 719,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            } else if (paragraph.includes('[') && paragraph.includes('](')) {\n                                                                                // Handle links\n                                                                                const linkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n                                                                                const parts = paragraph.split(linkRegex);\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-200 mb-4\",\n                                                                                    children: parts.map((part, i)=>{\n                                                                                        if (i % 3 === 1) {\n                                                                                            // This is link text\n                                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                                href: parts[i + 1],\n                                                                                                target: \"_blank\",\n                                                                                                rel: \"noopener noreferrer\",\n                                                                                                className: \"text-blue-400 hover:text-blue-300 underline transition-colors\",\n                                                                                                children: part\n                                                                                            }, i, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                                lineNumber: 733,\n                                                                                                columnNumber: 43\n                                                                                            }, this);\n                                                                                        } else if (i % 3 === 2) {\n                                                                                            // This is the URL (skip it as it's already used)\n                                                                                            return null;\n                                                                                        } else {\n                                                                                            // Regular text\n                                                                                            return part;\n                                                                                        }\n                                                                                    })\n                                                                                }, index, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 728,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            } else if (paragraph.trim() === '') {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-4\"\n                                                                                }, index, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 754,\n                                                                                    columnNumber: 40\n                                                                                }, this);\n                                                                            } else {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-200 mb-4\",\n                                                                                    children: paragraph\n                                                                                }, index, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                                    lineNumber: 757,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            }\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                        lineNumber: 682,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"complete-panel\", true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog-generator/page.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogGeneratorPage, \"kA9Y9G3tsIII3jTCImjqivnC7nI=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = BlogGeneratorPage;\nvar _c;\n$RefreshReg$(_c, \"BlogGeneratorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYmxvZy1nZW5lcmF0b3IvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ1k7QUFDdUY7QUFDbEg7QUFDZ0I7QUFDRDtBQUNYO0FBRWpCLFNBQVNrQjs7SUFDdEIsTUFBTSxFQUFFQyxNQUFNQyxPQUFPLEVBQUVDLE1BQU0sRUFBRSxHQUFHTiwyREFBVUE7SUFDNUMsTUFBTU8sU0FBU04sMERBQVNBO0lBRXhCLE1BQU0sQ0FBQ08sVUFBVUMsWUFBWSxHQUFHeEIsK0NBQVFBLENBQUM7UUFDdkN5QixPQUFPO1FBQ1BDLE9BQU87UUFDUEMsV0FBVztRQUNYQyxNQUFNO1FBQ05DLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxZQUFZO0lBQ2Q7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdqQywrQ0FBUUEsQ0FBQyxRQUFRLGdEQUFnRDs7SUFDdkcsTUFBTSxDQUFDa0MsYUFBYUMsZUFBZSxHQUFHbkMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDb0MsY0FBY0MsZ0JBQWdCLEdBQUdyQywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNzQyxrQkFBa0JDLG9CQUFvQixHQUFHdkMsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDd0MsaUJBQWlCQyxtQkFBbUIsR0FBR3pDLCtDQUFRQSxDQUFRLEVBQUU7SUFDaEUsTUFBTSxDQUFDMEMsaUJBQWlCQyxtQkFBbUIsR0FBRzNDLCtDQUFRQSxDQUFNO0lBQzVELE1BQU0sQ0FBQzRDLGNBQWNDLGdCQUFnQixHQUFHN0MsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDOEMsZUFBZUMsaUJBQWlCLEdBQUcvQywrQ0FBUUEsQ0FBVyxFQUFFO0lBQy9ELE1BQU0sQ0FBQ2dELHFCQUFxQkMsdUJBQXVCLEdBQUdqRCwrQ0FBUUEsQ0FBQztJQUUvRCx5Q0FBeUM7SUFDekNDLGdEQUFTQTt1Q0FBQztZQUNSLElBQUlvQixXQUFXLG1CQUFtQjtnQkFDaENDLE9BQU80QixJQUFJLENBQUM7WUFDZDtRQUNGO3NDQUFHO1FBQUM3QjtRQUFRQztLQUFPO0lBRW5CLDZCQUE2QjtJQUM3QnJCLGdEQUFTQTt1Q0FBQztZQUNSLElBQUlrRDtZQUNKLElBQUlqQixlQUFlYyxzQkFBc0JGLGNBQWNNLE1BQU0sR0FBRyxHQUFHO2dCQUNqRSw2Q0FBNkM7Z0JBQzdDLE1BQU1DLFVBQVU7b0JBQUM7b0JBQU07b0JBQU07b0JBQU07b0JBQU07aUJBQUssQ0FBQyxtQ0FBbUM7O2dCQUNsRixNQUFNQyxnQkFBZ0JELE9BQU8sQ0FBQ0wsb0JBQW9CLElBQUk7Z0JBRXRERyxXQUFXSTttREFBVzt3QkFDcEJOOzJEQUF1Qk8sQ0FBQUEsT0FBUUMsS0FBS0MsR0FBRyxDQUFDRixPQUFPLEdBQUdWLGNBQWNNLE1BQU0sR0FBRzs7b0JBQzNFO2tEQUFHRTtZQUNMO1lBQ0E7K0NBQU8sSUFBTUssYUFBYVI7O1FBQzVCO3NDQUFHO1FBQUNqQjtRQUFhYztRQUFxQkYsY0FBY00sTUFBTTtLQUFDO0lBRTNELDhDQUE4QztJQUM5QyxNQUFNUSx3QkFBd0I7UUFDNUIsSUFBSTVCLGdCQUFnQixRQUFRLE9BQU87UUFDbkMsSUFBSUEsZ0JBQWdCLGFBQWEsT0FBT3lCLEtBQUtDLEdBQUcsQ0FBQyxzQkFBdUJaLGNBQWNNLE1BQU0sR0FBSSxJQUFJO1FBQ3BHLElBQUlwQixnQkFBZ0IsY0FBYyxPQUFPO1FBQ3pDLElBQUlBLGdCQUFnQixZQUFZLE9BQU87UUFDdkMsT0FBTztJQUNUO0lBRUEsTUFBTTZCLG1CQUFtQjtRQUN2QixJQUFJN0IsZ0JBQWdCLGFBQWEsT0FBTztRQUN4QyxJQUFJQSxnQkFBZ0IsY0FBYyxPQUFPO1FBQ3pDLE9BQU87SUFDVDtJQUVBLGdCQUFnQjtJQUNoQixJQUFJWCxXQUFXLFdBQVc7UUFDeEIscUJBQ0UsOERBQUN5QztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJckM7SUFFQSxvQ0FBb0M7SUFDcEMsSUFBSTFDLFdBQVcsbUJBQW1CO1FBQ2hDLE9BQU87SUFDVDtJQUVBLHVCQUF1QjtJQUN2QixNQUFNNEMsZ0JBQWdCLE9BQU9DO1FBQzNCQSxFQUFFQyxjQUFjO1FBQ2hCLElBQUksQ0FBQzVDLFNBQVNFLEtBQUssRUFBRTtRQUVyQlUsZUFBZTtRQUNmRixlQUFlO1FBQ2ZjLGlCQUFpQjtZQUNmO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RFLHVCQUF1QjtRQUV2QixJQUFJO1lBQ0YsdUJBQXVCO1lBQ3ZCLE1BQU1tQixtQkFBbUIsTUFBTUMsTUFBTSwrQkFBK0I7Z0JBQ2xFQyxRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CakQsT0FBT0YsU0FBU0UsS0FBSztvQkFDckJJLGVBQWVOLFNBQVNNLGFBQWE7b0JBQ3JDRSxZQUFZUixTQUFTUSxVQUFVO2dCQUNqQztZQUNGO1lBRUEsTUFBTTRDLGVBQWUsTUFBTVAsaUJBQWlCUSxJQUFJO1lBQ2hELElBQUksQ0FBQ0QsYUFBYUUsT0FBTyxFQUFFO2dCQUN6QkMsTUFBTSxzQkFBc0JILGFBQWFJLEtBQUs7Z0JBQzlDOUMsZUFBZTtnQkFDZkUsZUFBZTtnQkFDZjtZQUNGO1lBRUEsK0NBQStDO1lBQy9DRixlQUFlO1lBQ2ZJLGdCQUFnQjtZQUVoQixNQUFNMkMscUJBQXFCLE1BQU1YLE1BQU0sZ0NBQWdDO2dCQUNyRUMsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQmpELE9BQU9GLFNBQVNFLEtBQUs7b0JBQ3JCSSxlQUFlTixTQUFTTSxhQUFhO29CQUNyQ0YsV0FBV0osU0FBU0ksU0FBUztvQkFDN0JDLE1BQU1MLFNBQVNLLElBQUk7b0JBQ25CRixPQUFPSCxTQUFTRyxLQUFLO29CQUNyQkksZ0JBQWdCUCxTQUFTTyxjQUFjO29CQUN2Q21ELFlBQVlOLGFBQWFPLFFBQVEsQ0FBQ0QsVUFBVTtvQkFDNUNOLGNBQWNBLGFBQWF4RCxJQUFJO29CQUMvQmdFLGtCQUFrQjtvQkFDbEJDLHdCQUF3QjtnQkFDMUI7WUFDRjtZQUVBLE1BQU1DLGlCQUFpQixNQUFNTCxtQkFBbUJKLElBQUk7WUFDcEQsSUFBSVMsZUFBZVIsT0FBTyxFQUFFO2dCQUMxQnRDLG9CQUFvQjhDLGVBQWVDLE9BQU87Z0JBQzFDN0MsbUJBQW1CNEMsZUFBZTdDLGVBQWUsSUFBSSxFQUFFO2dCQUN2REcsbUJBQW1CMEMsZUFBZUgsUUFBUSxJQUFJLENBQUM7Z0JBQy9DakQsZUFBZTtZQUNqQixPQUFPO2dCQUNMNkMsTUFBTSx3QkFBd0JPLGVBQWVOLEtBQUs7Z0JBQ2xEOUMsZUFBZTtZQUNqQjtRQUNGLEVBQUUsT0FBTzhDLE9BQU87WUFDZEQsTUFBTTtZQUNON0MsZUFBZTtRQUNqQixTQUFVO1lBQ1JFLGVBQWU7WUFDZkUsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTWtELGNBQWM7UUFDbEJ0RCxlQUFlO1FBQ2ZNLG9CQUFvQjtRQUNwQkUsbUJBQW1CLEVBQUU7UUFDckJFLG1CQUFtQjtRQUNuQm5CLFlBQVk7WUFDVkMsT0FBTztZQUNQQyxPQUFPO1lBQ1BDLFdBQVc7WUFDWEMsTUFBTTtZQUNOQyxlQUFlO1lBQ2ZDLGdCQUFnQjtZQUNoQkMsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxNQUFNeUQsa0JBQWtCO1FBQ3RCQyxVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ3JEO0lBQ2hDO0lBRUEsTUFBTXNELHFCQUFxQjtRQUN6QixNQUFNQyxPQUFPLElBQUlDLEtBQUs7WUFBQ3hEO1NBQWlCLEVBQUU7WUFBRXlELE1BQU07UUFBZ0I7UUFDbEUsTUFBTUMsTUFBTUMsSUFBSUMsZUFBZSxDQUFDTDtRQUNoQyxNQUFNTSxJQUFJQyxTQUFTQyxhQUFhLENBQUM7UUFDakNGLEVBQUVHLElBQUksR0FBR047UUFDVEcsRUFBRUksUUFBUSxHQUFHLEdBQXFELE9BQWxEaEYsU0FBU0UsS0FBSyxDQUFDK0UsT0FBTyxDQUFDLFFBQVEsS0FBS0MsV0FBVyxJQUFHO1FBQ2xFTixFQUFFTyxLQUFLO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQzVDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUM3RCxpREFBTUEsQ0FBQzRELEdBQUc7d0JBQ1Q2QyxTQUFTOzRCQUNQQyxHQUFHO2dDQUFDO2dDQUFHO2dDQUFLOzZCQUFFOzRCQUNkQyxHQUFHO2dDQUFDO2dDQUFHLENBQUM7Z0NBQUs7NkJBQUU7d0JBQ2pCO3dCQUNBQyxZQUFZOzRCQUNWQyxVQUFVOzRCQUNWQyxRQUFRQzs0QkFDUkMsTUFBTTt3QkFDUjt3QkFDQW5ELFdBQVU7Ozs7OztrQ0FFWiw4REFBQzdELGlEQUFNQSxDQUFDNEQsR0FBRzt3QkFDVDZDLFNBQVM7NEJBQ1BDLEdBQUc7Z0NBQUM7Z0NBQUcsQ0FBQztnQ0FBSzs2QkFBRTs0QkFDZkMsR0FBRztnQ0FBQztnQ0FBRztnQ0FBSzs2QkFBRTt3QkFDaEI7d0JBQ0FDLFlBQVk7NEJBQ1ZDLFVBQVU7NEJBQ1ZDLFFBQVFDOzRCQUNSQyxNQUFNO3dCQUNSO3dCQUNBbkQsV0FBVTs7Ozs7Ozs7Ozs7OzBCQUtkLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDakQsa0RBQUlBO29EQUFDd0YsTUFBSzs4REFDVCw0RUFBQ3BHLGlEQUFNQSxDQUFDaUgsTUFBTTt3REFDWkMsWUFBWTs0REFBRUMsT0FBTzt3REFBSzt3REFDMUJ0RCxXQUFVO2tFQUVWLDRFQUFDeEQseUpBQVNBOzREQUFDd0QsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHekIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUMzRCx5SkFBUUE7Z0VBQUMyRCxXQUFVOzs7Ozs7Ozs7OztzRUFFdEIsOERBQUNEOzs4RUFDQyw4REFBQ3dEO29FQUFHdkQsV0FBVTs4RUFBZ0M7Ozs7Ozs4RUFDOUMsOERBQUNDO29FQUFFRCxXQUFVOzhFQUFnQjs7Ozs7O2dFQUM1QkYsb0NBQ0MsOERBQUNDO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDQzt3RUFBRUQsV0FBVTs7NEVBQXdCOzRFQUFpQkY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FROUQ3QixnQkFBZ0IsVUFBVUEsZ0JBQWdCLDRCQUMxQyw4REFBQzhCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDQzs0REFBRUQsV0FBVTs7Z0VBQ1YvQixnQkFBZ0IsZUFBZTtnRUFDL0JBLGdCQUFnQixnQkFBZ0I7Ozs7Ozs7c0VBRW5DLDhEQUFDZ0M7NERBQUVELFdBQVU7O2dFQUF5Qk4sS0FBSzhELEtBQUssQ0FBQzNEO2dFQUF5Qjs7Ozs7Ozs7Ozs7Ozs4REFFNUUsOERBQUNFO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3lEOzREQUFJekQsV0FBVTs7OEVBQ2IsOERBQUMwRDtvRUFDQ0MsSUFBRztvRUFDSEMsSUFBRztvRUFDSEMsR0FBRTtvRUFDRkMsUUFBTztvRUFDUEMsYUFBWTtvRUFDWkMsTUFBSzs7Ozs7OzhFQUVQLDhEQUFDTjtvRUFDQ0MsSUFBRztvRUFDSEMsSUFBRztvRUFDSEMsR0FBRTtvRUFDRkMsUUFBTztvRUFDUEMsYUFBWTtvRUFDWkMsTUFBSztvRUFDTEMsaUJBQWlCLEdBQW9CLE9BQWpCLElBQUl2RSxLQUFLd0UsRUFBRSxHQUFHO29FQUNsQ0Msa0JBQWtCLEdBQTBELE9BQXZELElBQUl6RSxLQUFLd0UsRUFBRSxHQUFHLEtBQU0sS0FBSXJFLDBCQUEwQixHQUFFO29FQUN6RUcsV0FBVTs7Ozs7Ozs7Ozs7O3NFQUdkLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ29FO2dFQUFLcEUsV0FBVTs7b0VBQ2JOLEtBQUs4RCxLQUFLLENBQUMzRDtvRUFBeUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQVVsREEsMEJBQTBCLEtBQUtBLDBCQUEwQixxQkFDeEQsOERBQUNFO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDN0QsaURBQU1BLENBQUM0RCxHQUFHO29DQUNUQyxXQUFVO29DQUNWcUUsU0FBUzt3Q0FBRUMsT0FBTztvQ0FBRTtvQ0FDcEIxQixTQUFTO3dDQUFFMEIsT0FBTyxHQUEyQixPQUF4QnpFLHlCQUF3QjtvQ0FBRztvQ0FDaERrRCxZQUFZO3dDQUFFQyxVQUFVO29DQUFJOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPcEMsOERBQUNqRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDN0QsaURBQU1BLENBQUM0RCxHQUFHO29DQUNUc0UsU0FBUzt3Q0FBRUUsU0FBUzt3Q0FBRzFCLEdBQUcsQ0FBQztvQ0FBRztvQ0FDOUJELFNBQVM7d0NBQUUyQixTQUFTO3dDQUFHMUIsR0FBRztvQ0FBRTtvQ0FDNUI3QyxXQUFVOzhDQUVWLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN3RTtnREFBR3hFLFdBQVU7O2tFQUNaLDhEQUFDckQseUpBQVFBO3dEQUFDcUQsV0FBVTs7Ozs7O29EQUErQjs7Ozs7OzswREFJckQsOERBQUN5RTtnREFBS0MsVUFBVXhFO2dEQUFlRixXQUFVOztrRUFFdkMsOERBQUNEOzswRUFDQyw4REFBQzRFO2dFQUFNM0UsV0FBVTswRUFBK0M7Ozs7OzswRUFHaEUsOERBQUM0RTtnRUFDQzVDLE1BQUs7Z0VBQ0w2QyxPQUFPckgsU0FBU0UsS0FBSztnRUFDckJvSCxVQUFVLENBQUMzRSxJQUFNMUMsWUFBWTt3RUFBQyxHQUFHRCxRQUFRO3dFQUFFRSxPQUFPeUMsRUFBRTRFLE1BQU0sQ0FBQ0YsS0FBSztvRUFBQTtnRUFDaEVHLGFBQVk7Z0VBQ1pDLFFBQVE7Z0VBQ1JqRixXQUFVOzs7Ozs7Ozs7Ozs7a0VBS2QsOERBQUNEOzswRUFDQyw4REFBQzRFO2dFQUFNM0UsV0FBVTswRUFBK0M7Ozs7OzswRUFHaEUsOERBQUM0RTtnRUFDQzVDLE1BQUs7Z0VBQ0w2QyxPQUFPckgsU0FBU0csS0FBSztnRUFDckJtSCxVQUFVLENBQUMzRSxJQUFNMUMsWUFBWTt3RUFBQyxHQUFHRCxRQUFRO3dFQUFFRyxPQUFPd0MsRUFBRTRFLE1BQU0sQ0FBQ0YsS0FBSztvRUFBQTtnRUFDaEVHLGFBQVk7Z0VBQ1poRixXQUFVOzs7Ozs7Ozs7Ozs7a0VBS2QsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7O2tGQUNDLDhEQUFDNEU7d0VBQU0zRSxXQUFVO2tGQUErQzs7Ozs7O2tGQUdoRSw4REFBQ2tGO3dFQUNDTCxPQUFPckgsU0FBU0ksU0FBUzt3RUFDekJrSCxVQUFVLENBQUMzRSxJQUFNMUMsWUFBWTtnRkFBQyxHQUFHRCxRQUFRO2dGQUFFSSxXQUFXdUgsU0FBU2hGLEVBQUU0RSxNQUFNLENBQUNGLEtBQUs7NEVBQUM7d0VBQzlFN0UsV0FBVTs7MEZBRVYsOERBQUNvRjtnRkFBT1AsT0FBTzswRkFBSzs7Ozs7OzBGQUNwQiw4REFBQ087Z0ZBQU9QLE9BQU87MEZBQU07Ozs7OzswRkFDckIsOERBQUNPO2dGQUFPUCxPQUFPOzBGQUFNOzs7Ozs7MEZBQ3JCLDhEQUFDTztnRkFBT1AsT0FBTzswRkFBTTs7Ozs7OzBGQUNyQiw4REFBQ087Z0ZBQU9QLE9BQU87MEZBQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFHekIsOERBQUM5RTs7a0ZBQ0MsOERBQUM0RTt3RUFBTTNFLFdBQVU7a0ZBQStDOzs7Ozs7a0ZBR2hFLDhEQUFDa0Y7d0VBQ0NMLE9BQU9ySCxTQUFTSyxJQUFJO3dFQUNwQmlILFVBQVUsQ0FBQzNFLElBQU0xQyxZQUFZO2dGQUFDLEdBQUdELFFBQVE7Z0ZBQUVLLE1BQU1zQyxFQUFFNEUsTUFBTSxDQUFDRixLQUFLOzRFQUFBO3dFQUMvRDdFLFdBQVU7OzBGQUVWLDhEQUFDb0Y7Z0ZBQU9QLE9BQU07MEZBQWU7Ozs7OzswRkFDN0IsOERBQUNPO2dGQUFPUCxPQUFNOzBGQUFTOzs7Ozs7MEZBQ3ZCLDhEQUFDTztnRkFBT1AsT0FBTTswRkFBaUI7Ozs7OzswRkFDL0IsOERBQUNPO2dGQUFPUCxPQUFNOzBGQUFnQjs7Ozs7OzBGQUM5Qiw4REFBQ087Z0ZBQU9QLE9BQU07MEZBQVc7Ozs7OzswRkFDekIsOERBQUNPO2dGQUFPUCxPQUFNOzBGQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBTS9CLDhEQUFDOUU7OzBFQUNDLDhEQUFDNEU7Z0VBQU0zRSxXQUFVOzBFQUErQzs7Ozs7OzBFQUdoRSw4REFBQ2tGO2dFQUNDTCxPQUFPckgsU0FBU1EsVUFBVTtnRUFDMUI4RyxVQUFVLENBQUMzRSxJQUFNMUMsWUFBWTt3RUFBQyxHQUFHRCxRQUFRO3dFQUFFUSxZQUFZbUgsU0FBU2hGLEVBQUU0RSxNQUFNLENBQUNGLEtBQUs7b0VBQUM7Z0VBQy9FN0UsV0FBVTs7a0ZBRVYsOERBQUNvRjt3RUFBT1AsT0FBTztrRkFBRzs7Ozs7O2tGQUNsQiw4REFBQ087d0VBQU9QLE9BQU87a0ZBQUk7Ozs7OztrRkFDbkIsOERBQUNPO3dFQUFPUCxPQUFPO2tGQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS3ZCLDhEQUFDekI7d0RBQ0NwQixNQUFLO3dEQUNMcUQsU0FBUyxJQUFNdkcsZ0JBQWdCLENBQUNEO3dEQUNoQ21CLFdBQVU7OzREQUVUbkIsZUFBZSxTQUFTOzREQUFPOzs7Ozs7O29EQUdqQ0EsOEJBQ0MsOERBQUMxQyxpREFBTUEsQ0FBQzRELEdBQUc7d0RBQ1RzRSxTQUFTOzREQUFFRSxTQUFTOzREQUFHZSxRQUFRO3dEQUFFO3dEQUNqQzFDLFNBQVM7NERBQUUyQixTQUFTOzREQUFHZSxRQUFRO3dEQUFPO3dEQUN0Q3RGLFdBQVU7OzBFQUVWLDhEQUFDRDs7a0ZBQ0MsOERBQUM0RTt3RUFBTTNFLFdBQVU7a0ZBQStDOzs7Ozs7a0ZBR2hFLDhEQUFDNEU7d0VBQ0M1QyxNQUFLO3dFQUNMNkMsT0FBT3JILFNBQVNNLGFBQWE7d0VBQzdCZ0gsVUFBVSxDQUFDM0UsSUFBTTFDLFlBQVk7Z0ZBQUMsR0FBR0QsUUFBUTtnRkFBRU0sZUFBZXFDLEVBQUU0RSxNQUFNLENBQUNGLEtBQUs7NEVBQUE7d0VBQ3hFRyxhQUFZO3dFQUNaaEYsV0FBVTs7Ozs7Ozs7Ozs7OzBFQUdkLDhEQUFDRDs7a0ZBQ0MsOERBQUM0RTt3RUFBTTNFLFdBQVU7a0ZBQStDOzs7Ozs7a0ZBR2hFLDhEQUFDNEU7d0VBQ0M1QyxNQUFLO3dFQUNMNkMsT0FBT3JILFNBQVNPLGNBQWM7d0VBQzlCK0csVUFBVSxDQUFDM0UsSUFBTTFDLFlBQVk7Z0ZBQUMsR0FBR0QsUUFBUTtnRkFBRU8sZ0JBQWdCb0MsRUFBRTRFLE1BQU0sQ0FBQ0YsS0FBSzs0RUFBQTt3RUFDekVHLGFBQVk7d0VBQ1poRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBT2xCLDhEQUFDN0QsaURBQU1BLENBQUNpSCxNQUFNO3dEQUNacEIsTUFBSzt3REFDTHVELFVBQVVwSCxlQUFlLENBQUNYLFNBQVNFLEtBQUs7d0RBQ3hDMkYsWUFBWTs0REFBRUMsT0FBTzt3REFBSzt3REFDMUJrQyxVQUFVOzREQUFFbEMsT0FBTzt3REFBSzt3REFDeEJ0RCxXQUFVO2tFQUVUN0IsNEJBQ0M7OzhFQUNFLDhEQUFDNEI7b0VBQUlDLFdBQVU7Ozs7Ozs4RUFDZiw4REFBQ29FOzhFQUFLOzs7Ozs7O3lGQUdSOzs4RUFDRSw4REFBQzdILDBKQUFHQTtvRUFBQ3lELFdBQVU7Ozs7Ozs4RUFDZiw4REFBQ29FOzhFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBU2xCLDhEQUFDakksaURBQU1BLENBQUM0RCxHQUFHO29DQUNUc0UsU0FBUzt3Q0FBRUUsU0FBUzt3Q0FBRzFCLEdBQUc7b0NBQUc7b0NBQzdCRCxTQUFTO3dDQUFFMkIsU0FBUzt3Q0FBRzFCLEdBQUc7b0NBQUU7b0NBQzVCN0MsV0FBVTs4Q0FFViw0RUFBQzVELDJEQUFlQTt3Q0FBQ3FKLE1BQUs7OzRDQUVuQnhILGdCQUFnQix3QkFDZiw4REFBQzlCLGlEQUFNQSxDQUFDNEQsR0FBRztnREFFVHNFLFNBQVM7b0RBQUVFLFNBQVM7Z0RBQUU7Z0RBQ3RCM0IsU0FBUztvREFBRTJCLFNBQVM7Z0RBQUU7Z0RBQ3RCbUIsTUFBTTtvREFBRW5CLFNBQVM7Z0RBQUU7Z0RBQ25CdkUsV0FBVTs7a0VBRVYsOERBQUN3RTt3REFBR3hFLFdBQVU7OzBFQUNaLDhEQUFDcEQsMEpBQU1BO2dFQUFDb0QsV0FBVTs7Ozs7OzREQUErQjs7Ozs7OztrRUFHbkQsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ3pELDBKQUFHQTtnRUFBQ3lELFdBQVU7Ozs7OzswRUFDZiw4REFBQ0M7MEVBQUU7Ozs7OzswRUFDSCw4REFBQ0E7Z0VBQUVELFdBQVU7MEVBQWU7Ozs7Ozs7Ozs7Ozs7K0NBYjFCOzs7Ozs0Q0FtQlAvQixnQkFBZ0IsNkJBQ2YsOERBQUM5QixpREFBTUEsQ0FBQzRELEdBQUc7Z0RBRVRzRSxTQUFTO29EQUFFRSxTQUFTO2dEQUFFO2dEQUN0QjNCLFNBQVM7b0RBQUUyQixTQUFTO2dEQUFFO2dEQUN0Qm1CLE1BQU07b0RBQUVuQixTQUFTO2dEQUFFO2dEQUNuQnZFLFdBQVU7O2tFQUVWLDhEQUFDd0U7d0RBQUd4RSxXQUFVOzswRUFDWiw4REFBQ2xELDBKQUFLQTtnRUFBQ2tELFdBQVU7Ozs7Ozs0REFBNEM7Ozs7Ozs7a0VBRy9ELDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDWmpCLGNBQWM0RyxHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQ3hCLDhEQUFDOUY7Z0VBQWdCQyxXQUFVOztvRUFDeEI2RixRQUFRNUcsb0NBQ1AsOERBQUNwQywwSkFBV0E7d0VBQUNtRCxXQUFVOzs7OzsrRUFDckI2RixVQUFVNUcsb0NBQ1osOERBQUNjO3dFQUFJQyxXQUFVOzs7Ozs2RkFFZiw4REFBQ0Q7d0VBQUlDLFdBQVU7Ozs7OztrRkFFakIsOERBQUNvRTt3RUFBS3BFLFdBQVc5Qyw4Q0FBRUEsQ0FDakIsV0FDQTJJLFNBQVM1RyxzQkFBc0IsZUFBZTtrRkFFN0MyRzs7Ozs7OzsrREFaS0M7Ozs7Ozs7Ozs7OytDQVpWOzs7Ozs0Q0FpQ1A1SCxnQkFBZ0IsOEJBQ2YsOERBQUM5QixpREFBTUEsQ0FBQzRELEdBQUc7Z0RBRVRzRSxTQUFTO29EQUFFRSxTQUFTO2dEQUFFO2dEQUN0QjNCLFNBQVM7b0RBQUUyQixTQUFTO2dEQUFFO2dEQUN0Qm1CLE1BQU07b0RBQUVuQixTQUFTO2dEQUFFO2dEQUNuQnZFLFdBQVU7O2tFQUVWLDhEQUFDd0U7d0RBQUd4RSxXQUFVOzswRUFDWiw4REFBQ3pELDBKQUFHQTtnRUFBQ3lELFdBQVU7Ozs7Ozs0REFBK0M7Ozs7Ozs7a0VBR2hFLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7MEVBQ2YsOERBQUNDOzBFQUFFOzs7Ozs7MEVBQ0gsOERBQUNBO2dFQUFFRCxXQUFVOzBFQUFlOzs7Ozs7Ozs7Ozs7OytDQWIxQjs7Ozs7NENBbUJQL0IsZ0JBQWdCLGNBQWNNLGtDQUM3Qiw4REFBQ3BDLGlEQUFNQSxDQUFDNEQsR0FBRztnREFFVHNFLFNBQVM7b0RBQUVFLFNBQVM7Z0RBQUU7Z0RBQ3RCM0IsU0FBUztvREFBRTJCLFNBQVM7Z0RBQUU7Z0RBQ3RCbUIsTUFBTTtvREFBRW5CLFNBQVM7Z0RBQUU7Z0RBQ25CdkUsV0FBVTs7a0VBR1YsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDd0U7d0VBQUd4RSxXQUFVOzswRkFDWiw4REFBQ25ELDBKQUFXQTtnRkFBQ21ELFdBQVU7Ozs7Ozs0RUFBZ0M7Ozs7Ozs7a0ZBR3pELDhEQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNvRDtnRkFDQ2lDLFNBQVM1RDtnRkFDVHpCLFdBQVU7Z0ZBQ1ZyQyxPQUFNOzBGQUVOLDRFQUFDbEIsMEpBQUlBO29GQUFDdUQsV0FBVTs7Ozs7Ozs7Ozs7MEZBRWxCLDhEQUFDb0Q7Z0ZBQ0NpQyxTQUFTeEQ7Z0ZBQ1Q3QixXQUFVO2dGQUNWckMsT0FBTTswRkFFTiw0RUFBQ2pCLDBKQUFRQTtvRkFBQ3NELFdBQVU7Ozs7Ozs7Ozs7OzBGQUV0Qiw4REFBQ29EO2dGQUNDaUMsU0FBUzdEO2dGQUNUeEIsV0FBVTswRkFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzREQU9KckIsaUNBQ0MsOERBQUNvQjtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7MEZBQWlDckIsZ0JBQWdCZixTQUFTLElBQUk7Ozs7OzswRkFDN0UsOERBQUNtQztnRkFBSUMsV0FBVTswRkFBd0I7Ozs7Ozs7Ozs7OztrRkFFekMsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7MEZBQXFDckIsZ0JBQWdCbUgsb0JBQW9CLElBQUk7Ozs7OzswRkFDNUYsOERBQUMvRjtnRkFBSUMsV0FBVTswRkFBd0I7Ozs7Ozs7Ozs7OztrRkFFekMsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7MEZBQW9DckIsZ0JBQWdCb0gsVUFBVSxJQUFJOzs7Ozs7MEZBQ2pGLDhEQUFDaEc7Z0ZBQUlDLFdBQVU7MEZBQXdCOzs7Ozs7Ozs7Ozs7a0ZBRXpDLDhEQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNEO2dGQUFJQyxXQUFVOzBGQUNackIsZ0JBQWdCcUgsWUFBWSxHQUFHLE1BQU07Ozs7OzswRkFFeEMsOERBQUNqRztnRkFBSUMsV0FBVTswRkFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvREFPOUN2QixnQkFBZ0JZLE1BQU0sR0FBRyxtQkFDeEIsOERBQUNVO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ2lHO2dFQUFHakcsV0FBVTs7a0ZBQ1osOERBQUMxRCwwSkFBTUE7d0VBQUMwRCxXQUFVOzs7Ozs7b0VBQStCO29FQUN6QnZCLGdCQUFnQlksTUFBTTtvRUFBQzs7Ozs7OzswRUFFakQsOERBQUNVO2dFQUFJQyxXQUFVOzBFQUNadkIsZ0JBQWdCa0gsR0FBRyxDQUFDLENBQUNPLFFBQVFMLHNCQUM1Qiw4REFBQzlGO3dFQUFnQkMsV0FBVTtrRkFDekIsNEVBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ0Q7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDbUc7NEZBQUduRyxXQUFVO3NHQUF5Q2tHLE9BQU92SSxLQUFLOzs7Ozs7c0dBQ25FLDhEQUFDeUU7NEZBQ0NHLE1BQU0yRCxPQUFPakUsR0FBRzs0RkFDaEI4QyxRQUFPOzRGQUNQcUIsS0FBSTs0RkFDSnBHLFdBQVU7c0dBRVRrRyxPQUFPakUsR0FBRzs7Ozs7O3dGQUVaaUUsT0FBT0csU0FBUyxJQUFJSCxPQUFPRyxTQUFTLENBQUNoSCxNQUFNLEdBQUcsbUJBQzdDLDhEQUFDVTs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUNvRTtvR0FBS3BFLFdBQVU7OEdBQXdCOzs7Ozs7OEdBQ3hDLDhEQUFDb0U7b0dBQUtwRSxXQUFVOzhHQUF5QmtHLE9BQU9HLFNBQVMsQ0FBQ0MsSUFBSSxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEZBSXJFLDhEQUFDdkc7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDRDs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUNEO29HQUFJQyxXQUFVOzt3R0FBb0NrRyxPQUFPSyxZQUFZO3dHQUFDOzs7Ozs7OzhHQUN2RSw4REFBQ3hHO29HQUFJQyxXQUFVOzhHQUF3Qjs7Ozs7Ozs7Ozs7O3NHQUV6Qyw4REFBQ0Q7NEZBQUlDLFdBQVU7OzhHQUNiLDhEQUFDRDtvR0FBSUMsV0FBVTs4R0FBZ0NrRyxPQUFPdEksU0FBUzs7Ozs7OzhHQUMvRCw4REFBQ21DO29HQUFJQyxXQUFVOzhHQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VFQTFCckM2Rjs7Ozs7Ozs7Ozs7Ozs7OztrRUFxQ2xCLDhEQUFDOUY7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ2lHO29FQUFHakcsV0FBVTs7c0ZBQ1osOERBQUMzRCx5SkFBUUE7NEVBQUMyRCxXQUFVOzs7Ozs7d0VBQWlCOzs7Ozs7Ozs7Ozs7MEVBSXpDLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUNEO3dFQUNDQyxXQUFVO3dFQUNWd0csT0FBTzs0RUFDTEMsWUFBWTs0RUFDWkMsWUFBWTs0RUFDWkMsVUFBVTs0RUFDVkMsWUFBWTt3RUFDZDtrRkFFQ3JJLGlCQUFpQnNJLEtBQUssQ0FBQyxNQUFNbEIsR0FBRyxDQUFDLENBQUNtQixXQUFXakI7NEVBQzVDLHFDQUFxQzs0RUFDckMsSUFBSWlCLFVBQVVDLFVBQVUsQ0FBQyxPQUFPO2dGQUM5QixxQkFDRSw4REFBQ3hEO29GQUFldkQsV0FBVTs4RkFDdkI4RyxVQUFVckUsT0FBTyxDQUFDLE1BQU07bUZBRGxCb0Q7Ozs7OzRFQUliLE9BQU8sSUFBSWlCLFVBQVVDLFVBQVUsQ0FBQyxRQUFRO2dGQUN0QyxxQkFDRSw4REFBQ3ZDO29GQUFleEUsV0FBVTs4RkFDdkI4RyxVQUFVckUsT0FBTyxDQUFDLE9BQU87bUZBRG5Cb0Q7Ozs7OzRFQUliLE9BQU8sSUFBSWlCLFVBQVVDLFVBQVUsQ0FBQyxTQUFTO2dGQUN2QyxxQkFDRSw4REFBQ2Q7b0ZBQWVqRyxXQUFVOzhGQUN2QjhHLFVBQVVyRSxPQUFPLENBQUMsUUFBUTttRkFEcEJvRDs7Ozs7NEVBSWIsT0FBTyxJQUFJaUIsVUFBVUMsVUFBVSxDQUFDLFNBQVNELFVBQVVDLFVBQVUsQ0FBQyxPQUFPO2dGQUNuRSxxQkFDRSw4REFBQ0M7b0ZBQWVoSCxXQUFVOzhGQUN2QjhHLFVBQVVyRSxPQUFPLENBQUMsVUFBVTttRkFEdEJvRDs7Ozs7NEVBSWIsT0FBTyxJQUFJaUIsVUFBVUcsS0FBSyxDQUFDLFlBQVk7Z0ZBQ3JDLHFCQUNFLDhEQUFDRDtvRkFBZWhILFdBQVU7OEZBQ3ZCOEcsVUFBVXJFLE9BQU8sQ0FBQyxXQUFXO21GQUR2Qm9EOzs7Ozs0RUFJYixPQUFPLElBQUlpQixVQUFVSSxRQUFRLENBQUMsUUFBUUosVUFBVUksUUFBUSxDQUFDLE9BQU87Z0ZBQzlELGVBQWU7Z0ZBQ2YsTUFBTUMsWUFBWTtnRkFDbEIsTUFBTUMsUUFBUU4sVUFBVUQsS0FBSyxDQUFDTTtnRkFDOUIscUJBQ0UsOERBQUNsSDtvRkFBY0QsV0FBVTs4RkFDdEJvSCxNQUFNekIsR0FBRyxDQUFDLENBQUMwQixNQUFNQzt3RkFDaEIsSUFBSUEsSUFBSSxNQUFNLEdBQUc7NEZBQ2Ysb0JBQW9COzRGQUNwQixxQkFDRSw4REFBQ2xGO2dHQUVDRyxNQUFNNkUsS0FBSyxDQUFDRSxJQUFJLEVBQUU7Z0dBQ2xCdkMsUUFBTztnR0FDUHFCLEtBQUk7Z0dBQ0pwRyxXQUFVOzBHQUVUcUg7K0ZBTklDOzs7Ozt3RkFTWCxPQUFPLElBQUlBLElBQUksTUFBTSxHQUFHOzRGQUN0QixpREFBaUQ7NEZBQ2pELE9BQU87d0ZBQ1QsT0FBTzs0RkFDTCxlQUFlOzRGQUNmLE9BQU9EO3dGQUNUO29GQUNGO21GQXRCTXhCOzs7Ozs0RUF5QlosT0FBTyxJQUFJaUIsVUFBVVMsSUFBSSxPQUFPLElBQUk7Z0ZBQ2xDLHFCQUFPLDhEQUFDeEg7b0ZBQWdCQyxXQUFVO21GQUFqQjZGOzs7Ozs0RUFDbkIsT0FBTztnRkFDTCxxQkFDRSw4REFBQzVGO29GQUFjRCxXQUFVOzhGQUN0QjhHO21GQURLakI7Ozs7OzRFQUlaO3dFQUNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQ0FyTUo7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQW1OeEI7R0E3dkJ3QjFJOztRQUNZSCx1REFBVUE7UUFDN0JDLHNEQUFTQTs7O0tBRkZFIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9ibG9nLWdlbmVyYXRvci9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IHsgRmlsZVRleHQsIFNlYXJjaCwgWmFwLCBBcnJvd0xlZnQsIENvcHksIERvd25sb2FkLCBTcGFya2xlcywgVHJlbmRpbmdVcCwgVGFyZ2V0LCBDaGVja0NpcmNsZSwgQWxlcnRDaXJjbGUsIENsb2NrIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgdXNlU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEJsb2dHZW5lcmF0b3JQYWdlKCkge1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24sIHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIFxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICB0b3BpYzogJycsXG4gICAgdGl0bGU6ICcnLFxuICAgIHdvcmRDb3VudDogMjAwMCxcbiAgICB0b25lOiAncHJvZmVzc2lvbmFsJyxcbiAgICB0YXJnZXRLZXl3b3JkOiAnJyxcbiAgICB0YXJnZXRBdWRpZW5jZTogJycsXG4gICAgbnVtUmVzdWx0czogMTBcbiAgfSlcbiAgXG4gIC8vIE11bHRpLXN0ZXAgc3RhdGVzXG4gIGNvbnN0IFtjdXJyZW50U3RlcCwgc2V0Q3VycmVudFN0ZXBdID0gdXNlU3RhdGUoJ2Zvcm0nKSAvLyAnZm9ybScsICdhbmFseXppbmcnLCAnZ2VuZXJhdGluZycsICdjb21wbGV0ZSdcbiAgY29uc3QgW2lzQW5hbHl6aW5nLCBzZXRJc0FuYWx5emluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2lzR2VuZXJhdGluZywgc2V0SXNHZW5lcmF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbZ2VuZXJhdGVkQ29udGVudCwgc2V0R2VuZXJhdGVkQ29udGVudF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2V4dGVybmFsU291cmNlcywgc2V0RXh0ZXJuYWxTb3VyY2VzXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcbiAgY29uc3QgW2NvbnRlbnRNZXRhZGF0YSwgc2V0Q29udGVudE1ldGFkYXRhXSA9IHVzZVN0YXRlPGFueT4obnVsbClcbiAgY29uc3QgW3Nob3dBZHZhbmNlZCwgc2V0U2hvd0FkdmFuY2VkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbcHJvZ3Jlc3NTdGVwcywgc2V0UHJvZ3Jlc3NTdGVwc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pXG4gIGNvbnN0IFtjdXJyZW50UHJvZ3Jlc3NTdGVwLCBzZXRDdXJyZW50UHJvZ3Jlc3NTdGVwXSA9IHVzZVN0YXRlKDApXG5cbiAgLy8gUmVkaXJlY3QgdG8gbG9naW4gaWYgbm90IGF1dGhlbnRpY2F0ZWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc3RhdHVzID09PSAndW5hdXRoZW50aWNhdGVkJykge1xuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpXG4gICAgfVxuICB9LCBbc3RhdHVzLCByb3V0ZXJdKVxuXG4gIC8vIEVuaGFuY2VkIHByb2dyZXNzIHRyYWNraW5nXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbGV0IGludGVydmFsOiBOb2RlSlMuVGltZW91dFxuICAgIGlmIChpc0FuYWx5emluZyAmJiBjdXJyZW50UHJvZ3Jlc3NTdGVwIDwgcHJvZ3Jlc3NTdGVwcy5sZW5ndGggLSAxKSB7XG4gICAgICAvLyBWYXJpYWJsZSB0aW1pbmcgYmFzZWQgb24gYWN0dWFsIEFQSSBwaGFzZXNcbiAgICAgIGNvbnN0IHRpbWluZ3MgPSBbNDAwMCwgODAwMCwgMzAwMCwgNjAwMCwgMjAwMF0gLy8gUmVhbGlzdGljIHRpbWluZ3MgZm9yIGVhY2ggcGhhc2VcbiAgICAgIGNvbnN0IGN1cnJlbnRUaW1pbmcgPSB0aW1pbmdzW2N1cnJlbnRQcm9ncmVzc1N0ZXBdIHx8IDMwMDBcbiAgICAgIFxuICAgICAgaW50ZXJ2YWwgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc2V0Q3VycmVudFByb2dyZXNzU3RlcChwcmV2ID0+IE1hdGgubWluKHByZXYgKyAxLCBwcm9ncmVzc1N0ZXBzLmxlbmd0aCAtIDEpKVxuICAgICAgfSwgY3VycmVudFRpbWluZylcbiAgICB9XG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dChpbnRlcnZhbClcbiAgfSwgW2lzQW5hbHl6aW5nLCBjdXJyZW50UHJvZ3Jlc3NTdGVwLCBwcm9ncmVzc1N0ZXBzLmxlbmd0aF0pXG5cbiAgLy8gUHJvZ3Jlc3MgdHJhY2tpbmcgd2l0aCBlc3RpbWF0ZWQgY29tcGxldGlvblxuICBjb25zdCBnZXRQcm9ncmVzc1BlcmNlbnRhZ2UgPSAoKSA9PiB7XG4gICAgaWYgKGN1cnJlbnRTdGVwID09PSAnZm9ybScpIHJldHVybiAwXG4gICAgaWYgKGN1cnJlbnRTdGVwID09PSAnYW5hbHl6aW5nJykgcmV0dXJuIE1hdGgubWluKChjdXJyZW50UHJvZ3Jlc3NTdGVwIC8gcHJvZ3Jlc3NTdGVwcy5sZW5ndGgpICogNzAsIDY1KVxuICAgIGlmIChjdXJyZW50U3RlcCA9PT0gJ2dlbmVyYXRpbmcnKSByZXR1cm4gODVcbiAgICBpZiAoY3VycmVudFN0ZXAgPT09ICdjb21wbGV0ZScpIHJldHVybiAxMDBcbiAgICByZXR1cm4gMFxuICB9XG5cbiAgY29uc3QgZ2V0RXN0aW1hdGVkVGltZSA9ICgpID0+IHtcbiAgICBpZiAoY3VycmVudFN0ZXAgPT09ICdhbmFseXppbmcnKSByZXR1cm4gJzMtNCBtaW51dGVzJ1xuICAgIGlmIChjdXJyZW50U3RlcCA9PT0gJ2dlbmVyYXRpbmcnKSByZXR1cm4gJzEtMiBtaW51dGVzJ1xuICAgIHJldHVybiBudWxsXG4gIH1cblxuICAvLyBMb2FkaW5nIHN0YXRlXG4gIGlmIChzdGF0dXMgPT09ICdsb2FkaW5nJykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ibGFjayBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHctMTIgaC0xMiBib3JkZXItMiBib3JkZXItcGluay00MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+TG9hZGluZy4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyBEb24ndCByZW5kZXIgaWYgbm90IGF1dGhlbnRpY2F0ZWRcbiAgaWYgKHN0YXR1cyA9PT0gJ3VuYXV0aGVudGljYXRlZCcpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgLy8gU3RhcnQgYW5hbHlzaXMgcGhhc2VcbiAgY29uc3QgaGFuZGxlQW5hbHl6ZSA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICBpZiAoIWZvcm1EYXRhLnRvcGljKSByZXR1cm5cbiAgICBcbiAgICBzZXRJc0FuYWx5emluZyh0cnVlKVxuICAgIHNldEN1cnJlbnRTdGVwKCdhbmFseXppbmcnKVxuICAgIHNldFByb2dyZXNzU3RlcHMoW1xuICAgICAgJ0NvbmR1Y3RpbmcgVGF2aWx5IHNlYXJjaC4uLicsXG4gICAgICAnRXh0cmFjdGluZyBjb250ZW50IGZyb20gdG9wIHBhZ2VzLi4uJyxcbiAgICAgICdTdG9yaW5nIGluIGtub3dsZWRnZSBiYXNlLi4uJyxcbiAgICAgICdBbmFseXppbmcgY29tcGV0aXRvcnMuLi4nLFxuICAgICAgJ0dlbmVyYXRpbmcgaW5zaWdodHMuLi4nLFxuICAgICAgJ0NyZWF0aW5nIG9wdGltaXplZCBjb250ZW50Li4uJ1xuICAgIF0pXG4gICAgc2V0Q3VycmVudFByb2dyZXNzU3RlcCgwKVxuICAgIFxuICAgIHRyeSB7XG4gICAgICAvLyBTdGVwIDE6IFJ1biBhbmFseXNpc1xuICAgICAgY29uc3QgYW5hbHlzaXNSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2Jsb2ctZ2VuZXJhdG9yL2FuYWx5emUnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHRvcGljOiBmb3JtRGF0YS50b3BpYyxcbiAgICAgICAgICB0YXJnZXRLZXl3b3JkOiBmb3JtRGF0YS50YXJnZXRLZXl3b3JkLFxuICAgICAgICAgIG51bVJlc3VsdHM6IGZvcm1EYXRhLm51bVJlc3VsdHNcbiAgICAgICAgfSlcbiAgICAgIH0pXG4gICAgICBcbiAgICAgIGNvbnN0IGFuYWx5c2lzRGF0YSA9IGF3YWl0IGFuYWx5c2lzUmVzcG9uc2UuanNvbigpXG4gICAgICBpZiAoIWFuYWx5c2lzRGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIGFsZXJ0KCdBbmFseXNpcyBmYWlsZWQ6ICcgKyBhbmFseXNpc0RhdGEuZXJyb3IpXG4gICAgICAgIHNldEN1cnJlbnRTdGVwKCdmb3JtJylcbiAgICAgICAgc2V0SXNBbmFseXppbmcoZmFsc2UpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBTdGVwIDI6IEltbWVkaWF0ZWx5IHN0YXJ0IGNvbnRlbnQgZ2VuZXJhdGlvblxuICAgICAgc2V0Q3VycmVudFN0ZXAoJ2dlbmVyYXRpbmcnKVxuICAgICAgc2V0SXNHZW5lcmF0aW5nKHRydWUpXG4gICAgICBcbiAgICAgIGNvbnN0IGdlbmVyYXRpb25SZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2Jsb2ctZ2VuZXJhdG9yL2dlbmVyYXRlJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICB0b3BpYzogZm9ybURhdGEudG9waWMsXG4gICAgICAgICAgdGFyZ2V0S2V5d29yZDogZm9ybURhdGEudGFyZ2V0S2V5d29yZCxcbiAgICAgICAgICB3b3JkQ291bnQ6IGZvcm1EYXRhLndvcmRDb3VudCxcbiAgICAgICAgICB0b25lOiBmb3JtRGF0YS50b25lLFxuICAgICAgICAgIHRpdGxlOiBmb3JtRGF0YS50aXRsZSxcbiAgICAgICAgICB0YXJnZXRBdWRpZW5jZTogZm9ybURhdGEudGFyZ2V0QXVkaWVuY2UsXG4gICAgICAgICAgYW5hbHlzaXNJZDogYW5hbHlzaXNEYXRhLm1ldGFkYXRhLmFuYWx5c2lzSWQsXG4gICAgICAgICAgYW5hbHlzaXNEYXRhOiBhbmFseXNpc0RhdGEuZGF0YSwgLy8gUGFzcyB0aGUgY29tcGxldGUgYW5hbHlzaXMgZGF0YVxuICAgICAgICAgIHVzZUtub3dsZWRnZUJhc2U6IHRydWUsXG4gICAgICAgICAgdXNlQ29tcGV0aXRpdmVBbmFseXNpczogdHJ1ZVxuICAgICAgICB9KVxuICAgICAgfSlcbiAgICAgIFxuICAgICAgY29uc3QgZ2VuZXJhdGlvbkRhdGEgPSBhd2FpdCBnZW5lcmF0aW9uUmVzcG9uc2UuanNvbigpXG4gICAgICBpZiAoZ2VuZXJhdGlvbkRhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRHZW5lcmF0ZWRDb250ZW50KGdlbmVyYXRpb25EYXRhLmNvbnRlbnQpXG4gICAgICAgIHNldEV4dGVybmFsU291cmNlcyhnZW5lcmF0aW9uRGF0YS5leHRlcm5hbFNvdXJjZXMgfHwgW10pXG4gICAgICAgIHNldENvbnRlbnRNZXRhZGF0YShnZW5lcmF0aW9uRGF0YS5tZXRhZGF0YSB8fCB7fSlcbiAgICAgICAgc2V0Q3VycmVudFN0ZXAoJ2NvbXBsZXRlJylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGFsZXJ0KCdHZW5lcmF0aW9uIGZhaWxlZDogJyArIGdlbmVyYXRpb25EYXRhLmVycm9yKVxuICAgICAgICBzZXRDdXJyZW50U3RlcCgnZm9ybScpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gZ2VuZXJhdGUgY29udGVudCcpXG4gICAgICBzZXRDdXJyZW50U3RlcCgnZm9ybScpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzQW5hbHl6aW5nKGZhbHNlKVxuICAgICAgc2V0SXNHZW5lcmF0aW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIC8vIFJlc2V0IHRvIGZvcm1cbiAgY29uc3QgaGFuZGxlUmVzZXQgPSAoKSA9PiB7XG4gICAgc2V0Q3VycmVudFN0ZXAoJ2Zvcm0nKVxuICAgIHNldEdlbmVyYXRlZENvbnRlbnQoJycpXG4gICAgc2V0RXh0ZXJuYWxTb3VyY2VzKFtdKVxuICAgIHNldENvbnRlbnRNZXRhZGF0YShudWxsKVxuICAgIHNldEZvcm1EYXRhKHtcbiAgICAgIHRvcGljOiAnJyxcbiAgICAgIHRpdGxlOiAnJyxcbiAgICAgIHdvcmRDb3VudDogMjAwMCxcbiAgICAgIHRvbmU6ICdwcm9mZXNzaW9uYWwnLFxuICAgICAgdGFyZ2V0S2V5d29yZDogJycsXG4gICAgICB0YXJnZXRBdWRpZW5jZTogJycsXG4gICAgICBudW1SZXN1bHRzOiAxMFxuICAgIH0pXG4gIH1cblxuICBjb25zdCBjb3B5VG9DbGlwYm9hcmQgPSAoKSA9PiB7XG4gICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQoZ2VuZXJhdGVkQ29udGVudClcbiAgfVxuXG4gIGNvbnN0IGRvd25sb2FkQXNNYXJrZG93biA9ICgpID0+IHtcbiAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2dlbmVyYXRlZENvbnRlbnRdLCB7IHR5cGU6ICd0ZXh0L21hcmtkb3duJyB9KVxuICAgIGNvbnN0IHVybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYilcbiAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpXG4gICAgYS5ocmVmID0gdXJsXG4gICAgYS5kb3dubG9hZCA9IGAke2Zvcm1EYXRhLnRvcGljLnJlcGxhY2UoL1xccysvZywgJy0nKS50b0xvd2VyQ2FzZSgpfS5tZGBcbiAgICBhLmNsaWNrKClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctYmxhY2tcIj5cbiAgICAgIHsvKiBBbmltYXRlZCBCYWNrZ3JvdW5kICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHotMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1waW5rLTkwMC8yMCB2aWEtYmxhY2sgdG8tcm9zZS05MDAvMjBcIiAvPlxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgIHg6IFswLCAxMDAsIDBdLFxuICAgICAgICAgICAgeTogWzAsIC0xMDAsIDBdLFxuICAgICAgICAgIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17e1xuICAgICAgICAgICAgZHVyYXRpb246IDIwLFxuICAgICAgICAgICAgcmVwZWF0OiBJbmZpbml0eSxcbiAgICAgICAgICAgIGVhc2U6IFwibGluZWFyXCJcbiAgICAgICAgICB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xLzQgbGVmdC0xLzQgdy1bNTAwcHhdIGgtWzUwMHB4XSBiZy1waW5rLTUwMC8xMCByb3VuZGVkLWZ1bGwgYmx1ci1bMTAwcHhdXCJcbiAgICAgICAgLz5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBhbmltYXRlPXt7XG4gICAgICAgICAgICB4OiBbMCwgLTEwMCwgMF0sXG4gICAgICAgICAgICB5OiBbMCwgMTAwLCAwXSxcbiAgICAgICAgICB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3tcbiAgICAgICAgICAgIGR1cmF0aW9uOiAxNSxcbiAgICAgICAgICAgIHJlcGVhdDogSW5maW5pdHksXG4gICAgICAgICAgICBlYXNlOiBcImxpbmVhclwiXG4gICAgICAgICAgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMS80IHJpZ2h0LTEvNCB3LVs2MDBweF0gaC1bNjAwcHhdIGJnLXJvc2UtNTAwLzEwIHJvdW5kZWQtZnVsbCBibHVyLVsxMjBweF1cIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItd2hpdGUvMTAgYmctYmxhY2svNjAgYmFja2Ryb3AtYmx1ci14bFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNiBweS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkXCI+XG4gICAgICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLXhsIGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBob3ZlcjpiZy13aGl0ZS8yMCB0cmFuc2l0aW9uLWFsbFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIHJvdW5kZWQtMnhsIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcGluay02MDAgdG8tcm9zZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPkVuaGFuY2VkIEJsb2cgR2VuZXJhdG9yPC9oMT5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPkFJLVBvd2VyZWQgU0VPIENvbnRlbnQgQ3JlYXRpb248L3A+XG4gICAgICAgICAgICAgICAgICAgIHtnZXRFc3RpbWF0ZWRUaW1lKCkgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1waW5rLTQwMFwiPkVzdGltYXRlZCB0aW1lOiB7Z2V0RXN0aW1hdGVkVGltZSgpfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIHsvKiBQcm9ncmVzcyBpbmRpY2F0b3IgKi99XG4gICAgICAgICAgICAgIHsoY3VycmVudFN0ZXAgIT09ICdmb3JtJyAmJiBjdXJyZW50U3RlcCAhPT0gJ2NvbXBsZXRlJykgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRTdGVwID09PSAnYW5hbHl6aW5nJyAmJiAnUmVzZWFyY2hpbmcgJiBBbmFseXppbmcuLi4nfVxuICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gJ2dlbmVyYXRpbmcnICYmICdXcml0aW5nIENvbnRlbnQuLi4nfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPntNYXRoLnJvdW5kKGdldFByb2dyZXNzUGVyY2VudGFnZSgpKX0lIGNvbXBsZXRlPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiByZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMTYgaC0xNiB0cmFuc2Zvcm0gLXJvdGF0ZS05MFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxjaXJjbGVcbiAgICAgICAgICAgICAgICAgICAgICAgIGN4PVwiMzJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgY3k9XCIzMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICByPVwiMjhcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwicmdiKDc1LCA4NSwgOTkpXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlXG4gICAgICAgICAgICAgICAgICAgICAgICBjeD1cIjMyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGN5PVwiMzJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgcj1cIjI4XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZT1cInJnYigyMzYsIDcyLCAxNTMpXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VEYXNoYXJyYXk9e2AkezIgKiBNYXRoLlBJICogMjh9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZURhc2hvZmZzZXQ9e2AkezIgKiBNYXRoLlBJICogMjggKiAoMSAtIGdldFByb2dyZXNzUGVyY2VudGFnZSgpIC8gMTAwKX1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge01hdGgucm91bmQoZ2V0UHJvZ3Jlc3NQZXJjZW50YWdlKCkpfSVcbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICB7LyogR2xvYmFsIHByb2dyZXNzIGJhciAqL31cbiAgICAgICAgICB7Z2V0UHJvZ3Jlc3NQZXJjZW50YWdlKCkgPiAwICYmIGdldFByb2dyZXNzUGVyY2VudGFnZSgpIDwgMTAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0xIGJnLWdyYXktODAwXCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1waW5rLTUwMCB0by1yb3NlLTUwMFwiXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyB3aWR0aDogMCB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgd2lkdGg6IGAke2dldFByb2dyZXNzUGVyY2VudGFnZSgpfSVgIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41IH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTYgcHktOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtOFwiPlxuICAgICAgICAgICAgey8qIEZvcm0gUGFuZWwgKi99XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS02XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS81IGJhY2tkcm9wLWJsdXIteGwgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLTJ4bCBwLTZcIj5cbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNiBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMiB0ZXh0LXBpbmstNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIENvbnRlbnQgQ29uZmlndXJhdGlvblxuICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZUFuYWx5emV9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgey8qIFRvcGljICovfVxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQmxvZyBUb3BpYyAqXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudG9waWN9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7Li4uZm9ybURhdGEsIHRvcGljOiBlLnRhcmdldC52YWx1ZX0pfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBibG9nIHRvcGljLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYmctd2hpdGUvMTAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLXhsIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6Ymctd2hpdGUvMjAgZm9jdXM6Ym9yZGVyLXBpbmstNTAwLzUwIHRyYW5zaXRpb24tYWxsXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogVGl0bGUgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBDdXN0b20gVGl0bGUgKE9wdGlvbmFsKVxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoey4uLmZvcm1EYXRhLCB0aXRsZTogZS50YXJnZXQudmFsdWV9KX1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkxlYXZlIGJsYW5rIGZvciBBSS1nZW5lcmF0ZWQgdGl0bGVcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYmctd2hpdGUvMTAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLXhsIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6Ymctd2hpdGUvMjAgZm9jdXM6Ym9yZGVyLXBpbmstNTAwLzUwIHRyYW5zaXRpb24tYWxsXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogV29yZCBDb3VudCBhbmQgVG9uZSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgV29yZCBDb3VudFxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLndvcmRDb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoey4uLmZvcm1EYXRhLCB3b3JkQ291bnQ6IHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKX0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBiZy13aGl0ZS8xMCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQteGwgdGV4dC13aGl0ZSBmb2N1czpiZy13aGl0ZS8yMCBmb2N1czpib3JkZXItcGluay01MDAvNTAgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezUwMH0+NTAwIHdvcmRzPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPXsxMDAwfT4xLDAwMCB3b3Jkczwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17MTUwMH0+MSw1MDAgd29yZHM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezIwMDB9PjIsMDAwIHdvcmRzPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPXszMDAwfT4zLDAwMCB3b3Jkczwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFRvbmVcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50b25lfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7Li4uZm9ybURhdGEsIHRvbmU6IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLXdoaXRlLzEwIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC14bCB0ZXh0LXdoaXRlIGZvY3VzOmJnLXdoaXRlLzIwIGZvY3VzOmJvcmRlci1waW5rLTUwMC81MCB0cmFuc2l0aW9uLWFsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInByb2Zlc3Npb25hbFwiPlByb2Zlc3Npb25hbDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImNhc3VhbFwiPkNhc3VhbDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImNvbnZlcnNhdGlvbmFsXCI+Q29udmVyc2F0aW9uYWw8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhdXRob3JpdGF0aXZlXCI+QXV0aG9yaXRhdGl2ZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImZyaWVuZGx5XCI+RnJpZW5kbHk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhY2FkZW1pY1wiPkFjYWRlbWljPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBOdW1iZXIgb2YgUmVzdWx0cyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIFJlc2VhcmNoIERlcHRoXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubnVtUmVzdWx0c31cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsuLi5mb3JtRGF0YSwgbnVtUmVzdWx0czogcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpfSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBiZy13aGl0ZS8xMCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQteGwgdGV4dC13aGl0ZSBmb2N1czpiZy13aGl0ZS8yMCBmb2N1czpib3JkZXItcGluay01MDAvNTAgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17NX0+NSBzb3VyY2VzIChGYXN0KTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezEwfT4xMCBzb3VyY2VzIChSZWNvbW1lbmRlZCk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPXsxNX0+MTUgc291cmNlcyAoQ29tcHJlaGVuc2l2ZSk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEFkdmFuY2VkIE9wdGlvbnMgKi99XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QWR2YW5jZWQoIXNob3dBZHZhbmNlZCl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWxlZnQgdGV4dC1waW5rLTQwMCBob3Zlcjp0ZXh0LXBpbmstMzAwIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3Nob3dBZHZhbmNlZCA/ICdIaWRlJyA6ICdTaG93J30gQWR2YW5jZWQgT3B0aW9uc1xuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgIHtzaG93QWR2YW5jZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgaGVpZ2h0OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBoZWlnaHQ6ICdhdXRvJyB9fVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktNFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFRhcmdldCBLZXl3b3JkXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRhcmdldEtleXdvcmR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEoey4uLmZvcm1EYXRhLCB0YXJnZXRLZXl3b3JkOiBlLnRhcmdldC52YWx1ZX0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlByaW1hcnkgU0VPIGtleXdvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLXdoaXRlLzEwIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC14bCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwIGZvY3VzOmJnLXdoaXRlLzIwIGZvY3VzOmJvcmRlci1waW5rLTUwMC81MCB0cmFuc2l0aW9uLWFsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgVGFyZ2V0IEF1ZGllbmNlXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRhcmdldEF1ZGllbmNlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsuLi5mb3JtRGF0YSwgdGFyZ2V0QXVkaWVuY2U6IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiV2hvIGlzIHRoaXMgY29udGVudCBmb3I/XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBiZy13aGl0ZS8xMCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQteGwgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMCBmb2N1czpiZy13aGl0ZS8yMCBmb2N1czpib3JkZXItcGluay01MDAvNTAgdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgey8qIEFuYWx5emUgQnV0dG9uICovfVxuICAgICAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0FuYWx5emluZyB8fCAhZm9ybURhdGEudG9waWN9XG4gICAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDIgfX1cbiAgICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB5LTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXBpbmstNjAwIHRvLXJvc2UtNjAwIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCByb3VuZGVkLXhsIGhvdmVyOmZyb20tcGluay03MDAgaG92ZXI6dG8tcm9zZS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdHJhbnNpdGlvbi1hbGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2lzQW5hbHl6aW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC01IHctNSBib3JkZXItYi0yIGJvcmRlci13aGl0ZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Q3JlYXRpbmcgQ29udGVudC4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkdlbmVyYXRlIEFydGljbGU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5idXR0b24+XG4gICAgICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgey8qIE11bHRpLVN0ZXAgT3V0cHV0IFBhbmVsICovfVxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS02XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEFuaW1hdGVQcmVzZW5jZSBtb2RlPVwid2FpdFwiPlxuICAgICAgICAgICAgICAgIHsvKiBGb3JtIFN0ZXAgKi99XG4gICAgICAgICAgICAgICAge2N1cnJlbnRTdGVwID09PSAnZm9ybScgJiYgKFxuICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PVwiZm9ybS1wYW5lbFwiXG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtMnhsIHAtNlwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi02IGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhcmdldCBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTIgdGV4dC1waW5rLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgUmVhZHkgdG8gR2VuZXJhdGVcbiAgICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMiB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgbXgtYXV0byBtYi00IG9wYWNpdHktNTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxwPkZpbGwgb3V0IHRoZSBmb3JtIGFuZCBjbGljayBcIkdlbmVyYXRlIEFydGljbGVcIiB0byBiZWdpbjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIG10LTJcIj5XZSdsbCByZXNlYXJjaCwgYW5hbHl6ZSwgYW5kIGNyZWF0ZSB5b3VyIGNvbnRlbnQgYXV0b21hdGljYWxseTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIHsvKiBBbmFseXppbmcgU3RlcCAqL31cbiAgICAgICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09ICdhbmFseXppbmcnICYmIChcbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT1cImFuYWx5emluZy1wYW5lbFwiXG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtMnhsIHAtNlwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi02IGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cInctNSBoLTUgbXItMiB0ZXh0LXBpbmstNDAwIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgQW5hbHl6aW5nIFRvcGljXG4gICAgICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAge3Byb2dyZXNzU3RlcHMubWFwKChzdGVwLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2luZGV4IDwgY3VycmVudFByb2dyZXNzU3RlcCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyZWVuLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiBpbmRleCA9PT0gY3VycmVudFByb2dyZXNzU3RlcCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNSBoLTUgYm9yZGVyLTIgYm9yZGVyLXBpbmstNDAwIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy01IGgtNSBib3JkZXItMiBib3JkZXItZ3JheS02MDAgcm91bmRlZC1mdWxsXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcInRleHQtc21cIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleCA8PSBjdXJyZW50UHJvZ3Jlc3NTdGVwID8gXCJ0ZXh0LXdoaXRlXCIgOiBcInRleHQtZ3JheS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RlcH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7LyogR2VuZXJhdGluZyBTdGVwICovfVxuICAgICAgICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gJ2dlbmVyYXRpbmcnICYmIChcbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT1cImdlbmVyYXRpbmctcGFuZWxcIlxuICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZS81IGJhY2tkcm9wLWJsdXIteGwgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLTJ4bCBwLTZcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItNiBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxaYXAgY2xhc3NOYW1lPVwidy01IGgtNSBtci0yIHRleHQteWVsbG93LTQwMCBhbmltYXRlLXB1bHNlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBXcml0aW5nIFlvdXIgQXJ0aWNsZVxuICAgICAgICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiB3LTEyIGgtMTIgYm9yZGVyLTIgYm9yZGVyLXBpbmstNDAwIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cD5DcmVhdGluZyB5b3VyIFNFTy1vcHRpbWl6ZWQgYXJ0aWNsZS4uLjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIG10LTJcIj5Vc2luZyByZXNlYXJjaCBpbnNpZ2h0cyBhbmQgbWFya2V0IGludGVsbGlnZW5jZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIHsvKiBDb21wbGV0ZSBTdGVwIC0gRW5oYW5jZWQgQXJ0aWNsZSBWaWV3ICovfVxuICAgICAgICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gJ2NvbXBsZXRlJyAmJiBnZW5lcmF0ZWRDb250ZW50ICYmIChcbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT1cImNvbXBsZXRlLXBhbmVsXCJcbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS02XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgey8qIEFydGljbGUgSGVhZGVyIHdpdGggTWV0YWRhdGEgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSBiYWNrZHJvcC1ibHVyLXhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC0yeGwgcC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTIgdGV4dC1ncmVlbi00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBBcnRpY2xlIEdlbmVyYXRlZCBTdWNjZXNzZnVsbHlcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjb3B5VG9DbGlwYm9hcmR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGJnLXdoaXRlLzEwIGhvdmVyOmJnLXdoaXRlLzIwIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyB0b29sdGlwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkNvcHkgdG8gQ2xpcGJvYXJkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb3B5IGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17ZG93bmxvYWRBc01hcmtkb3dufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiBiZy13aGl0ZS8xMCBob3ZlcjpiZy13aGl0ZS8yMCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgdG9vbHRpcFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEb3dubG9hZCBhcyBNYXJrZG93blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZXNldH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctcGluay02MDAgaG92ZXI6YmctcGluay03MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgTmV3IEFydGljbGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICB7LyogQ29udGVudCBNZXRhZGF0YSAqL31cbiAgICAgICAgICAgICAgICAgICAgICB7Y29udGVudE1ldGFkYXRhICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSByb3VuZGVkLWxnIHAtMyB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj57Y29udGVudE1ldGFkYXRhLndvcmRDb3VudCB8fCAwfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+V29yZHM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvNSByb3VuZGVkLWxnIHAtMyB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNDAwXCI+e2NvbnRlbnRNZXRhZGF0YS5leHRlcm5hbFNvdXJjZXNDb3VudCB8fCAwfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+U291cmNlczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS81IHJvdW5kZWQtbGcgcC0zIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTQwMFwiPntjb250ZW50TWV0YWRhdGEudG9rZW5zVXNlZCB8fCAwfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+VG9rZW5zPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgcm91bmRlZC1sZyBwLTMgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb250ZW50TWV0YWRhdGEuZGF0YUVuaGFuY2VkID8gJ+KckycgOiAn4peLJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPkRhdGEgRW5oYW5jZWQ8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogRXh0ZXJuYWwgU291cmNlcyBQYW5lbCAqL31cbiAgICAgICAgICAgICAgICAgICAge2V4dGVybmFsU291cmNlcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtMnhsIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTIgdGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFJlc2VhcmNoIFNvdXJjZXMgVXNlZCAoe2V4dGVybmFsU291cmNlcy5sZW5ndGh9KVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZXh0ZXJuYWxTb3VyY2VzLm1hcCgoc291cmNlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiYmctd2hpdGUvNSByb3VuZGVkLWxnIHAtNCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSB0ZXh0LXNtIG1iLTFcIj57c291cmNlLnRpdGxlfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtzb3VyY2UudXJsfSBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwIGhvdmVyOnRleHQtYmx1ZS0zMDAgdGV4dC14cyBicmVhay1hbGwgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzb3VyY2UudXJsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c291cmNlLnN0cmVuZ3RocyAmJiBzb3VyY2Uuc3RyZW5ndGhzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPktleSBTdHJlbmd0aHM6IDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktMzAwXCI+e3NvdXJjZS5zdHJlbmd0aHMuam9pbignLCAnKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWwtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LWJvbGQgdGV4dC1ncmVlbi00MDBcIj57c291cmNlLnF1YWxpdHlTY29yZX0vMTAwPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlF1YWxpdHk8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtd2hpdGVcIj57c291cmNlLndvcmRDb3VudH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+V29yZHM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICB7LyogRW5oYW5jZWQgQXJ0aWNsZSBEaXNwbGF5ICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzUgYmFja2Ryb3AtYmx1ci14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtMnhsIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXBpbmstNjAwLzIwIHRvLXJvc2UtNjAwLzIwIHB4LTYgcHktNCBib3JkZXItYiBib3JkZXItd2hpdGUvMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy01IGgtNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgR2VuZXJhdGVkIEFydGljbGVcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJvc2UgcHJvc2UtaW52ZXJ0IHByb3NlLXBpbmsgbWF4LXctbm9uZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS0xMDAgbGVhZGluZy1yZWxheGVkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdoaXRlU3BhY2U6ICdwcmUtd3JhcCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiAndWktc2VyaWYsIEdlb3JnaWEsIENhbWJyaWEsIFwiVGltZXMgTmV3IFJvbWFuXCIsIFRpbWVzLCBzZXJpZicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE2cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogJzEuNydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2dlbmVyYXRlZENvbnRlbnQuc3BsaXQoJ1xcbicpLm1hcCgocGFyYWdyYXBoLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gSGFuZGxlIGRpZmZlcmVudCBtYXJrZG93biBlbGVtZW50c1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmFncmFwaC5zdGFydHNXaXRoKCcjICcpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgxIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG10LTggbWItNCBmaXJzdDptdC0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cGFyYWdyYXBoLnJlcGxhY2UoJyMgJywgJycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocGFyYWdyYXBoLnN0YXJ0c1dpdGgoJyMjICcpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgyIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtdC02IG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwYXJhZ3JhcGgucmVwbGFjZSgnIyMgJywgJycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocGFyYWdyYXBoLnN0YXJ0c1dpdGgoJyMjIyAnKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtdC01IG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwYXJhZ3JhcGgucmVwbGFjZSgnIyMjICcsICcnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHBhcmFncmFwaC5zdGFydHNXaXRoKCctICcpIHx8IHBhcmFncmFwaC5zdGFydHNXaXRoKCcqICcpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQtZ3JheS0yMDAgbWwtNCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cGFyYWdyYXBoLnJlcGxhY2UoL15bKi1dIC8sICcnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHBhcmFncmFwaC5tYXRjaCgvXlxcZCtcXC4gLykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTIwMCBtbC00IG1iLTEgbGlzdC1kZWNpbWFsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cGFyYWdyYXBoLnJlcGxhY2UoL15cXGQrXFwuIC8sICcnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHBhcmFncmFwaC5pbmNsdWRlcygnWycpICYmIHBhcmFncmFwaC5pbmNsdWRlcygnXSgnKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBIYW5kbGUgbGlua3NcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbGlua1JlZ2V4ID0gL1xcWyhbXlxcXV0rKVxcXVxcKChbXildKylcXCkvZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwYXJ0cyA9IHBhcmFncmFwaC5zcGxpdChsaW5rUmVnZXgpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTIwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cGFydHMubWFwKChwYXJ0LCBpKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpICUgMyA9PT0gMSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgbGluayB0ZXh0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17cGFydHNbaSArIDFdfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTQwMCBob3Zlcjp0ZXh0LWJsdWUtMzAwIHVuZGVybGluZSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwYXJ0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChpICUgMyA9PT0gMikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgdGhlIFVSTCAoc2tpcCBpdCBhcyBpdCdzIGFscmVhZHkgdXNlZClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFJlZ3VsYXIgdGV4dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBwYXJ0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChwYXJhZ3JhcGgudHJpbSgpID09PSAnJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMjAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwYXJhZ3JhcGh9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59ICJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsIkZpbGVUZXh0IiwiU2VhcmNoIiwiWmFwIiwiQXJyb3dMZWZ0IiwiQ29weSIsIkRvd25sb2FkIiwiU3BhcmtsZXMiLCJUYXJnZXQiLCJDaGVja0NpcmNsZSIsIkNsb2NrIiwiTGluayIsInVzZVNlc3Npb24iLCJ1c2VSb3V0ZXIiLCJjbiIsIkJsb2dHZW5lcmF0b3JQYWdlIiwiZGF0YSIsInNlc3Npb24iLCJzdGF0dXMiLCJyb3V0ZXIiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwidG9waWMiLCJ0aXRsZSIsIndvcmRDb3VudCIsInRvbmUiLCJ0YXJnZXRLZXl3b3JkIiwidGFyZ2V0QXVkaWVuY2UiLCJudW1SZXN1bHRzIiwiY3VycmVudFN0ZXAiLCJzZXRDdXJyZW50U3RlcCIsImlzQW5hbHl6aW5nIiwic2V0SXNBbmFseXppbmciLCJpc0dlbmVyYXRpbmciLCJzZXRJc0dlbmVyYXRpbmciLCJnZW5lcmF0ZWRDb250ZW50Iiwic2V0R2VuZXJhdGVkQ29udGVudCIsImV4dGVybmFsU291cmNlcyIsInNldEV4dGVybmFsU291cmNlcyIsImNvbnRlbnRNZXRhZGF0YSIsInNldENvbnRlbnRNZXRhZGF0YSIsInNob3dBZHZhbmNlZCIsInNldFNob3dBZHZhbmNlZCIsInByb2dyZXNzU3RlcHMiLCJzZXRQcm9ncmVzc1N0ZXBzIiwiY3VycmVudFByb2dyZXNzU3RlcCIsInNldEN1cnJlbnRQcm9ncmVzc1N0ZXAiLCJwdXNoIiwiaW50ZXJ2YWwiLCJsZW5ndGgiLCJ0aW1pbmdzIiwiY3VycmVudFRpbWluZyIsInNldFRpbWVvdXQiLCJwcmV2IiwiTWF0aCIsIm1pbiIsImNsZWFyVGltZW91dCIsImdldFByb2dyZXNzUGVyY2VudGFnZSIsImdldEVzdGltYXRlZFRpbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwiaGFuZGxlQW5hbHl6ZSIsImUiLCJwcmV2ZW50RGVmYXVsdCIsImFuYWx5c2lzUmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImFuYWx5c2lzRGF0YSIsImpzb24iLCJzdWNjZXNzIiwiYWxlcnQiLCJlcnJvciIsImdlbmVyYXRpb25SZXNwb25zZSIsImFuYWx5c2lzSWQiLCJtZXRhZGF0YSIsInVzZUtub3dsZWRnZUJhc2UiLCJ1c2VDb21wZXRpdGl2ZUFuYWx5c2lzIiwiZ2VuZXJhdGlvbkRhdGEiLCJjb250ZW50IiwiaGFuZGxlUmVzZXQiLCJjb3B5VG9DbGlwYm9hcmQiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJkb3dubG9hZEFzTWFya2Rvd24iLCJibG9iIiwiQmxvYiIsInR5cGUiLCJ1cmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJhIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImRvd25sb2FkIiwicmVwbGFjZSIsInRvTG93ZXJDYXNlIiwiY2xpY2siLCJhbmltYXRlIiwieCIsInkiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJyZXBlYXQiLCJJbmZpbml0eSIsImVhc2UiLCJidXR0b24iLCJ3aGlsZUhvdmVyIiwic2NhbGUiLCJoMSIsInJvdW5kIiwic3ZnIiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsImZpbGwiLCJzdHJva2VEYXNoYXJyYXkiLCJQSSIsInN0cm9rZURhc2hvZmZzZXQiLCJzcGFuIiwiaW5pdGlhbCIsIndpZHRoIiwib3BhY2l0eSIsImgyIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJpbnB1dCIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsInJlcXVpcmVkIiwic2VsZWN0IiwicGFyc2VJbnQiLCJvcHRpb24iLCJvbkNsaWNrIiwiaGVpZ2h0IiwiZGlzYWJsZWQiLCJ3aGlsZVRhcCIsIm1vZGUiLCJleGl0IiwibWFwIiwic3RlcCIsImluZGV4IiwiZXh0ZXJuYWxTb3VyY2VzQ291bnQiLCJ0b2tlbnNVc2VkIiwiZGF0YUVuaGFuY2VkIiwiaDMiLCJzb3VyY2UiLCJoNCIsInJlbCIsInN0cmVuZ3RocyIsImpvaW4iLCJxdWFsaXR5U2NvcmUiLCJzdHlsZSIsIndoaXRlU3BhY2UiLCJmb250RmFtaWx5IiwiZm9udFNpemUiLCJsaW5lSGVpZ2h0Iiwic3BsaXQiLCJwYXJhZ3JhcGgiLCJzdGFydHNXaXRoIiwibGkiLCJtYXRjaCIsImluY2x1ZGVzIiwibGlua1JlZ2V4IiwicGFydHMiLCJwYXJ0IiwiaSIsInRyaW0iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog-generator/page.tsx\n"));

/***/ })

});