/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/blog-generator/generate/route";
exports.ids = ["app/api/blog-generator/generate/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblog-generator%2Fgenerate%2Froute&page=%2Fapi%2Fblog-generator%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog-generator%2Fgenerate%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblog-generator%2Fgenerate%2Froute&page=%2Fapi%2Fblog-generator%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog-generator%2Fgenerate%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_blog_generator_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/blog-generator/generate/route.ts */ \"(rsc)/./src/app/api/blog-generator/generate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/blog-generator/generate/route\",\n        pathname: \"/api/blog-generator/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/blog-generator/generate/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/blog-generator/generate/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_blog_generator_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblog-generator%2Fgenerate%2Froute&page=%2Fapi%2Fblog-generator%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog-generator%2Fgenerate%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/blog-generator/generate/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/blog-generator/generate/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_quota__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/quota */ \"(rsc)/./src/lib/quota.ts\");\n/* harmony import */ var _lib_gemini__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/gemini */ \"(rsc)/./src/lib/gemini.ts\");\n\n\n\n\n\n/**\n * Enhanced Blog Generator Content Generation Endpoint\n * Phase 2: Generate content using knowledge base and competitive analysis\n */ async function POST(request) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        const userId = session.user.id;\n        const { topic, targetKeyword, wordCount = 2000, tone = 'professional', title, targetAudience, analysisId, useKnowledgeBase = true, useCompetitiveAnalysis = true, analysisData// Contains the research data from the analysis phase\n         } = await request.json();\n        if (!topic) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Topic is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`✍️ Starting enhanced content generation for: \"${topic}\"`);\n        let researchData = '';\n        let competitiveInsights = '';\n        let seoRecommendations = '';\n        let externalSources = [];\n        // Phase 1: Extract Research Data and External Sources\n        if (analysisData) {\n            console.log('📚 Phase 1: Processing analysis data for content generation...');\n            // Extract competitive analysis insights\n            if (analysisData.competitiveAnalysis) {\n                const compAnalysis = analysisData.competitiveAnalysis;\n                competitiveInsights = `\nCOMPETITIVE LANDSCAPE INSIGHTS:\n===============================\n\n📊 Market Analysis:\n• ${compAnalysis.competitorsAnalyzed} competitors analyzed\n• Average competitor quality score: ${Math.round(compAnalysis.averageCompetitorScore)}/100\n• Recommended word count: ${compAnalysis.recommendedWordCount} words\n\n🔍 Common Keywords Found:\n${compAnalysis.commonKeywords.map((kw)=>`• ${kw}`).join('\\n')}\n\n📈 Content Opportunities:\n${compAnalysis.contentGaps.map((gap)=>`• ${gap}`).join('\\n')}\n\n🚀 Strategic Advantages:\n${compAnalysis.opportunities.map((opp)=>`• ${opp}`).join('\\n')}\n\n📋 Content Strategy:\n${compAnalysis.contentStrategy.contentDepth}\nTarget Audience: ${compAnalysis.contentStrategy.targetAudience}\n`;\n            }\n            // Extract top competitor data and external sources\n            if (analysisData.topCompetitors) {\n                externalSources = analysisData.topCompetitors.map((comp)=>({\n                        url: comp.url,\n                        title: comp.title,\n                        score: comp.overallScore,\n                        wordCount: comp.wordCount,\n                        strengths: comp.strengths\n                    }));\n                researchData = `\nRESEARCH DATA FROM TOP SOURCES:\n==============================\n\n${analysisData.topCompetitors.map((comp, index)=>`\nSOURCE ${index + 1}: ${comp.title}\nURL: ${comp.url}\nQuality Score: ${comp.overallScore}/100\nWord Count: ${comp.wordCount} words\nKey Strengths: ${comp.strengths.join(', ')}\nContent Gaps: ${comp.gaps.join(', ')}\n`).join('\\n')}\n\nSEARCH INSIGHTS:\n===============\n• Total sources analyzed: ${analysisData.searchSummary.totalResults}\n• Successful extractions: ${analysisData.searchSummary.successfulExtractions}\n• Search query: \"${analysisData.searchSummary.searchQuery}\"\n`;\n            }\n            // Extract SEO insights\n            if (analysisData.seoInsights) {\n                const seoInsights = analysisData.seoInsights;\n                seoRecommendations = `\nSEO OPTIMIZATION STRATEGY:\n=========================\n\n🎯 Target Keyword Opportunity:\n${seoInsights.targetKeywordOpportunity ? `\n• Competitors using keyword: ${seoInsights.targetKeywordOpportunity.competitorsUsingKeyword}/${analysisData.competitiveAnalysis.competitorsAnalyzed}\n• Average title length: ${Math.round(seoInsights.targetKeywordOpportunity.averageTitleLength)} characters\n• Average meta description: ${Math.round(seoInsights.targetKeywordOpportunity.averageMetaLength)} characters\n` : 'Focus on natural keyword integration'}\n\n📝 Content Structure Recommendations:\n${seoInsights.commonHeadingPatterns.map((pattern)=>`• ${pattern}`).join('\\n')}\n\n🎯 Recommended Structure:\n${seoInsights.recommendedStructure.map((structure)=>`• ${structure}`).join('\\n')}\n`;\n            }\n            console.log(`✅ Processed analysis data: ${externalSources.length} sources, ${competitiveInsights.length} chars insights`);\n        }\n        // Phase 3: Generate Enhanced Content with Gemini\n        console.log('🤖 Phase 3: Generating content with Gemini 2.5 Flash Lite...');\n        const gemini = new _lib_gemini__WEBPACK_IMPORTED_MODULE_4__.GeminiService();\n        const enhancedPrompt = `\nYou are a world-class SEO content strategist and expert data-driven writer. Create a comprehensive, authoritative, and information-rich blog post packed with data, statistics, and credible external references.\n\nCONTENT REQUIREMENTS:\n====================\nTopic: ${topic}\nTarget Keyword: ${targetKeyword || 'Focus on topic naturally'}\nWord Count: ${wordCount} words (minimum)\nTone: ${tone}\nTarget Audience: ${targetAudience || 'General audience interested in the topic'}\n${title ? `Custom Title: ${title}` : 'Generate an optimal SEO title'}\n\nRESEARCH-BASED COMPETITIVE ANALYSIS:\n====================================\n${competitiveInsights}\n\nSEO OPTIMIZATION STRATEGY:\n==========================\n${seoRecommendations}\n\nCOMPREHENSIVE RESEARCH DATA:\n===========================\n${researchData}\n\nEXTERNAL SOURCES TO REFERENCE:\n==============================\n${externalSources.map((source, index)=>`\n${index + 1}. ${source.title}\n   URL: ${source.url}\n   Quality Score: ${source.score}/100\n   Word Count: ${source.wordCount} words\n   Key Strengths: ${source.strengths?.join(', ') || 'Comprehensive coverage'}\n`).join('\\n')}\n\nCONTENT GENERATION INSTRUCTIONS:\n===============================\n\n1. TITLE CREATION:\n   - Create a data-driven, compelling title (50-60 characters)\n   - Include target keyword naturally and add quantifiable elements (e.g., \"2025 Guide\", \"Top 10\", \"Complete Analysis\")\n   - Use power words like \"Ultimate\", \"Complete\", \"Data-Driven\", \"Comprehensive\"\n   - Make it click-worthy and authoritative\n\n2. INFORMATION-RICH CONTENT STRUCTURE:\n   - Start with a compelling hook using a relevant statistic or data point\n   - Write a comprehensive introduction with key statistics and what readers will learn\n   - Use clear H2 and H3 headings that include variations of the target keyword\n   - Create scannable content with data tables, comparison charts, and infographic-style sections\n   - Include extensive practical examples, case studies, and real-world applications\n   - Add a strong conclusion with actionable next steps and key takeaways\n\n3. DATA-DRIVEN CONTENT REQUIREMENTS:\n   - Include relevant statistics, percentages, and quantifiable data throughout\n   - Reference specific studies, surveys, and industry reports\n   - Provide market analysis with concrete numbers (user bases, adoption rates, pricing)\n   - Include performance benchmarks, comparison metrics, and measurable outcomes\n   - Use data to support every major claim and recommendation\n   - Create data visualizations through descriptive text (tables, charts, comparisons)\n\n4. EXTERNAL LINKS AND CREDIBILITY:\n   - Reference and link to the provided external sources strategically\n   - Format links as: [Link text](URL) - Brief description\n   - Include authoritative sources to back up statistics and claims\n   - Reference industry leaders, official documentation, and credible publications\n   - Ensure each major section has 1-2 relevant external references\n   - Balance between external credibility and content flow\n\n5. COMPREHENSIVE COMPETITIVE INTELLIGENCE:\n   - Integrate insights from the competitive analysis throughout\n   - Address all identified content gaps with detailed explanations\n   - Provide unique perspectives that differentiate from competitors\n   - Include market positioning and comparative analysis\n   - Offer strategic insights based on competitive landscape data\n\n6. ENHANCED SEO AND ENGAGEMENT:\n   - Use target keyword naturally throughout (1-2% density)\n   - Include semantic keywords and industry-specific terminology\n   - Create content that thoroughly answers user intent with data backing\n   - Include internal linking opportunities (mention as [INTERNAL LINK: anchor text])\n   - Write in active voice with industry expertise and data authority\n   - Include multiple calls-to-action with specific, measurable outcomes\n\nCONTENT FORMAT:\n==============\n- Use proper markdown formatting\n- Include a compelling title\n- Structure with clear headings and subheadings\n- Add bullet points and numbered lists for key information\n- Include tables for comparisons where appropriate\n- End with a strong call-to-action\n\nYour goal is to create the definitive resource on this topic that will naturally outrank all competitors through superior content quality, structure, and optimization.\n`;\n        const result = await gemini.generateContent(enhancedPrompt, {\n            temperature: 0.7,\n            maxOutputTokens: 8000,\n            thinkingConfig: {\n                thinkingBudget: -1,\n                includeThoughts: false\n            }\n        });\n        const generatedContent = result.response;\n        console.log(`✅ Content generated: ${generatedContent.length} characters`);\n        // Phase 4: Update quota usage\n        let updatedQuota;\n        try {\n            updatedQuota = await _lib_quota__WEBPACK_IMPORTED_MODULE_3__.QuotaManager.useQuota(userId, 'blog_posts');\n            console.log(`📊 Quota updated: ${updatedQuota.used}/${updatedQuota.limit} used`);\n        } catch (quotaError) {\n            console.warn('Failed to update quota:', quotaError);\n            updatedQuota = {\n                used: 1,\n                limit: 100\n            }; // Fallback\n        }\n        // Phase 5: Save generated content (optional - for future enhancements)\n        console.log('⏭️ Content generation complete, skipping storage for now');\n        console.log('✅ Enhanced blog generation complete!');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            content: generatedContent,\n            metadata: {\n                wordCount: generatedContent.split(/\\s+/).length,\n                tokensUsed: result.outputTokens,\n                researchDataUsed: researchData.length > 0,\n                competitiveAnalysisUsed: competitiveInsights.length > 0,\n                seoOptimized: true,\n                generationModel: 'gemini-2.5-flash-lite',\n                externalSourcesCount: externalSources.length,\n                dataEnhanced: true\n            },\n            externalSources: externalSources.map((source)=>({\n                    title: source.title,\n                    url: source.url,\n                    qualityScore: source.score,\n                    wordCount: source.wordCount,\n                    strengths: source.strengths || []\n                })),\n            quota: {\n                used: updatedQuota.used,\n                limit: updatedQuota.limit,\n                remaining: updatedQuota.limit === -1 ? -1 : updatedQuota.limit - updatedQuota.used\n            }\n        });\n    } catch (error) {\n        console.error('Enhanced blog generation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate enhanced blog content',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/blog-generator/generate/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async session ({ session, token }) {\n            if (session?.user && token?.sub) {\n                session.user.id = token.sub;\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        },\n        async signIn ({ user, account, profile }) {\n            return true // Let the adapter handle user creation\n            ;\n        }\n    },\n    events: {\n        async createUser ({ user }) {\n            try {\n                // Check if profile data already exists (to avoid duplicates)\n                const existingSettings = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.userSettings.findUnique({\n                    where: {\n                        userId: user.id\n                    }\n                });\n                if (!existingSettings) {\n                    // This runs after the adapter creates the user\n                    // Now we add the additional profile data\n                    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.update({\n                        where: {\n                            id: user.id\n                        },\n                        data: {\n                            firstName: user.name?.split(' ')[0] || '',\n                            lastName: user.name?.split(' ').slice(1).join(' ') || '',\n                            settings: {\n                                create: {\n                                }\n                            },\n                            subscription: {\n                                create: {\n                                    plan: 'free',\n                                    status: 'active'\n                                }\n                            },\n                            quotas: {\n                                create: [\n                                    {\n                                        quotaType: 'blog_posts',\n                                        totalLimit: 5,\n                                        resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)\n                                    },\n                                    {\n                                        quotaType: 'emails',\n                                        totalLimit: 10,\n                                        resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)\n                                    },\n                                    {\n                                        quotaType: 'social_media',\n                                        totalLimit: 20,\n                                        resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)\n                                    },\n                                    {\n                                        quotaType: 'youtube_scripts',\n                                        totalLimit: 3,\n                                        resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)\n                                    },\n                                    {\n                                        quotaType: 'invincible_research',\n                                        totalLimit: 2,\n                                        resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)\n                                    }\n                                ]\n                            }\n                        }\n                    });\n                    console.log(`✅ Created complete user profile for: ${user.email}`);\n                } else {\n                    console.log(`ℹ️ User profile already exists for: ${user.email}`);\n                }\n            } catch (error) {\n                console.error('Error setting up user profile:', error);\n            }\n        }\n    },\n    pages: {\n        signIn: '/login',\n        error: '/login?error=auth_error'\n    },\n    debug: \"development\" === 'development'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini.ts":
/*!***************************!*\
  !*** ./src/lib/gemini.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeminiService: () => (/* binding */ GeminiService)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(\"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\" || 0 || 0);\nclass GeminiService {\n    constructor(modelName = 'gemini-2.5-flash-lite-preview-06-17'){\n        this.model = genAI.getGenerativeModel({\n            model: modelName\n        });\n    }\n    /**\n   * Update model to use thinking-capable Gemini 2.5 models\n   */ updateModel(modelName) {\n        this.model = genAI.getGenerativeModel({\n            model: modelName\n        });\n    }\n    /**\n   * Estimate token count for text (rough approximation)\n   * Generally, 1 token ≈ 4 characters or 0.75 words\n   * Gemini 2.5 Flash-Lite Preview (2025): $0.10/M input, $0.40/M output (including thinking)\n   */ estimateTokenCount(text) {\n        // Use character count divided by 4 as a rough estimate\n        return Math.ceil(text.length / 4);\n    }\n    async generateContent(prompt, config = {}, logContext) {\n        const startTime = Date.now();\n        const callId = Math.random().toString(36).substr(2, 9);\n        // Enhanced logging for thinking-enabled generation\n        console.log(`💎 Gemini Content Call Started`);\n        console.log(`   📋 Call ID: ${callId}`);\n        console.log(`   🎬 Context: ${logContext || 'General Content'}`);\n        console.log(`   ⚙️ Model: ${this.model._model || 'gemini-2.5-flash-lite-preview-06-17'}`);\n        console.log(`   🌡️ Temperature: ${config.temperature || 0.7}`);\n        console.log(`   📏 Max Tokens: ${config.maxOutputTokens || 64000} (MAXIMUM FREEDOM)`);\n        console.log(`   🎯 TopP: ${config.topP || 0.95}`);\n        console.log(`   🔢 TopK: ${config.topK || 40}`);\n        console.log(`   📝 Prompt Length: ${prompt.length} chars`);\n        // Log thinking configuration\n        if (config.thinkingConfig) {\n            console.log(`   🧠 Thinking Enabled:`);\n            console.log(`      💭 Budget: ${config.thinkingConfig.thinkingBudget ?? 'dynamic'}`);\n            console.log(`      🔍 Include Thoughts: ${config.thinkingConfig.includeThoughts || false}`);\n        }\n        try {\n            const generationConfig = {\n                temperature: config.temperature || 0.7,\n                maxOutputTokens: config.maxOutputTokens || 64000,\n                topP: config.topP || 0.95,\n                topK: config.topK || 40\n            };\n            // Add thinking configuration if specified\n            if (config.thinkingConfig) {\n                generationConfig.thinkingConfig = {};\n                if (config.thinkingConfig.thinkingBudget !== undefined) {\n                    generationConfig.thinkingConfig.thinkingBudget = config.thinkingConfig.thinkingBudget;\n                }\n                if (config.thinkingConfig.includeThoughts !== undefined) {\n                    generationConfig.thinkingConfig.includeThoughts = config.thinkingConfig.includeThoughts;\n                }\n            }\n            console.log(`   📤 Sending request to Gemini...`);\n            const result = await this.model.generateContent({\n                contents: [\n                    {\n                        role: 'user',\n                        parts: [\n                            {\n                                text: prompt\n                            }\n                        ]\n                    }\n                ],\n                generationConfig\n            });\n            const response = await result.response;\n            const responseText = response.text();\n            // Extract thoughts if thinking was enabled\n            const thoughts = [];\n            let thoughtsTokenCount = 0;\n            if (config.thinkingConfig?.includeThoughts && response.candidates?.[0]?.content?.parts) {\n                for (const part of response.candidates[0].content.parts){\n                    if (part.thought && part.text) {\n                        thoughts.push(part.text);\n                    }\n                }\n            }\n            // Get thinking token count from usage metadata\n            if (response.usageMetadata?.thoughtsTokenCount) {\n                thoughtsTokenCount = response.usageMetadata.thoughtsTokenCount;\n            }\n            // Estimate token counts\n            const inputTokens = this.estimateTokenCount(prompt);\n            const outputTokens = this.estimateTokenCount(responseText);\n            const duration = Date.now() - startTime;\n            // Success logging\n            console.log(`   ✅ Gemini Content Complete`);\n            console.log(`   ⏱️ Duration: ${duration}ms`);\n            console.log(`   📊 Input Tokens: ${inputTokens} (estimated)`);\n            console.log(`   📊 Output Tokens: ${outputTokens} (estimated)`);\n            if (thoughtsTokenCount > 0) {\n                console.log(`   🧠 Thinking Tokens: ${thoughtsTokenCount}`);\n                console.log(`   💭 Thoughts Generated: ${thoughts.length}`);\n            }\n            console.log(`   📄 Response Length: ${responseText.length} chars`);\n            // Calculate cost including thinking tokens (Gemini 2.5 Flash-Lite Preview pricing)\n            const baseCost = inputTokens * 0.0000001 + outputTokens * 0.0000004; // $0.10/M input, $0.40/M output\n            const thinkingCost = thoughtsTokenCount * 0.0000004; // Thinking included in output rate\n            const totalCost = baseCost + thinkingCost;\n            console.log(`   💰 Estimated Cost: $${totalCost.toFixed(6)} (Flash-Lite 2025)`);\n            if (logContext?.includes('YouTube')) {\n                console.log(`   🎬 YouTube Content Success - Call ${callId}`);\n                console.log(`   📺 Step: ${logContext}`);\n            }\n            return {\n                response: responseText,\n                inputTokens,\n                outputTokens,\n                thoughtsTokenCount: thoughtsTokenCount > 0 ? thoughtsTokenCount : undefined,\n                thoughts: thoughts.length > 0 ? thoughts : undefined\n            };\n        } catch (error) {\n            const duration = Date.now() - startTime;\n            console.error(`   ❌ Gemini Content Failed`);\n            console.error(`   ⏱️ Failed after: ${duration}ms`);\n            console.error(`   📋 Call ID: ${callId}`);\n            console.error(`   🎬 Context: ${logContext || 'General Content'}`);\n            console.error(`   💥 Error:`, error);\n            if (logContext?.includes('YouTube')) {\n                console.error(`   🎬 YouTube Content FAILED - Call ${callId}`);\n                console.error(`   📺 Failed Step: ${logContext}`);\n            }\n            throw new Error(`Failed to generate content with Gemini: ${error}`);\n        }\n    }\n    /**\n   * Generate content with thinking enabled (convenience method)\n   */ async generateContentWithThinking(prompt, thinkingBudget = -1, includeThoughts = true, config = {}, logContext) {\n        return this.generateContent(prompt, {\n            ...config,\n            thinkingConfig: {\n                thinkingBudget,\n                includeThoughts\n            }\n        }, logContext);\n    }\n    /**\n   * Generate content with maximum thinking capability\n   */ async generateContentWithMaxThinking(prompt, config = {}, logContext) {\n        return this.generateContentWithThinking(prompt, 24576, true, config, logContext);\n    }\n    /**\n   * Generate content with thinking disabled\n   */ async generateContentWithoutThinking(prompt, config = {}, logContext) {\n        return this.generateContent(prompt, {\n            ...config,\n            thinkingConfig: {\n                thinkingBudget: 0,\n                includeThoughts: false\n            }\n        }, logContext);\n    }\n    async generateBlogPost(topic, wordCount, tone, researchData, competitionData, enableThinking = true) {\n        const prompt = `\nYou are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about \"${topic}\".\n\nCONTENT REQUIREMENTS:\n- Target word count: ${wordCount} words\n- Tone: ${tone}\n- Format: Professional markdown with proper headings, lists, and structure\n- Include compelling hook and engaging introduction\n- Use narrative storytelling and real-world examples\n- Include strategic call-to-action at the end\n- Write as a primary authoritative source\n- Use confident, authoritative language (avoid hedging)\n\nPROFESSIONAL WRITING STANDARDS:\n- Start with an attention-grabbing hook (question, statistic, or bold statement)\n- Create emotional connection with readers through storytelling\n- Use scannable formatting with headings, subheadings, and bullet points\n- Include actionable insights and practical advice\n- Incorporate relevant statistics and data points\n- Use active voice and strong verbs\n- Create smooth transitions between sections\n- End with a powerful conclusion and clear next steps\n\n${competitionData?.title ? `Article Title: ${competitionData.title}\\n` : ''}\n${competitionData?.targetKeyword ? `Target Keyword: ${competitionData.targetKeyword} (use naturally throughout the content)\\n` : ''}\n${competitionData?.targetAudience ? `Target Audience: ${competitionData.targetAudience} (tailor content for this audience)\\n` : ''}\n${competitionData?.competitors ? `Competitors to outperform: ${competitionData.competitors} (create content that surpasses these sources)\\n` : ''}\n\n${researchData ? `Research Data to incorporate:\\n${researchData}\\n` : ''}\n\nCONTENT STRUCTURE:\n1. Compelling Hook (question, statistic, or bold statement)\n2. Introduction with context and thesis\n3. Main sections with clear headings and subheadings\n4. Practical examples and case studies\n5. Actionable takeaways and recommendations\n6. Powerful conclusion with call-to-action\n\nCreate content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.\n`;\n        const config = enableThinking ? {\n            thinkingConfig: {\n                thinkingBudget: -1,\n                includeThoughts: false\n            }\n        } : {\n            thinkingConfig: {\n                thinkingBudget: 0\n            }\n        };\n        const result = await this.generateContent(prompt, {\n            temperature: 0.7,\n            maxOutputTokens: 64000,\n            ...config\n        });\n        return result.response;\n    }\n    async generateEmail(purpose, audience, tone, keyPoints, enableThinking = false) {\n        const prompt = `\nCreate a professional email for the following:\n\nPurpose: ${purpose}\nTarget Audience: ${audience}\nTone: ${tone}\nKey Points to Include: ${keyPoints.join(', ')}\n\nRequirements:\n- Include compelling subject line\n- Professional email structure (greeting, body, closing)\n- Clear call-to-action\n- Appropriate tone and language for the audience\n- Concise but comprehensive\n\nFormat the response as:\nSubject: [Subject Line]\n\n[Email Body]\n`;\n        const config = enableThinking ? {\n            thinkingConfig: {\n                thinkingBudget: 512,\n                includeThoughts: false\n            }\n        } : {\n            thinkingConfig: {\n                thinkingBudget: 0\n            }\n        };\n        const result = await this.generateContent(prompt, {\n            temperature: 0.6,\n            maxOutputTokens: 1500,\n            ...config\n        });\n        return result.response;\n    }\n    async generateTweet(topic, style, includeHashtags = true, enableThinking = false) {\n        const prompt = `\nCreate an engaging Twitter/X tweet about \"${topic}\".\n\nStyle: ${style}\nInclude hashtags: ${includeHashtags}\n\nRequirements:\n- Maximum 280 characters\n- Engaging and shareable\n- Include relevant emojis if appropriate\n- ${includeHashtags ? 'Include 2-3 relevant hashtags' : 'No hashtags'}\n- Hook the reader's attention\n- Encourage engagement (likes, retweets, replies)\n\nCreate a tweet that stands out in the feed and drives engagement.\n`;\n        const config = enableThinking ? {\n            thinkingConfig: {\n                thinkingBudget: 256,\n                includeThoughts: false\n            }\n        } : {\n            thinkingConfig: {\n                thinkingBudget: 0\n            }\n        };\n        const result = await this.generateContent(prompt, {\n            temperature: 0.8,\n            maxOutputTokens: 500,\n            ...config\n        });\n        return result.response;\n    }\n    async extractKeywords(topic, enableThinking = false) {\n        const prompt = `\nExtract the most important keywords from this topic for Google search: \"${topic}\"\n\nRequirements:\n- If the topic is a single word or simple phrase, use it as the main keyword\n- For complex topics, extract 3-5 key terms that best represent the topic\n- Focus on the main concepts and important terms\n- Use words that would be effective for Google search\n- Return only the keywords separated by spaces, nothing else\n- Do not include common words like \"the\", \"and\", \"of\", etc.\n- Do not add words like \"meaning\", \"definition\", \"example\" unless they are part of the original topic\n- Focus on specific, searchable terms from the original topic\n\nExamples:\nTopic: \"magistral\"\nKeywords: magistral\n\nTopic: \"How to build a React application with TypeScript\"\nKeywords: React TypeScript application build development\n\nTopic: \"artificial intelligence in healthcare\"\nKeywords: artificial intelligence healthcare\n\nReturn only the keywords:\n`;\n        const config = enableThinking ? {\n            thinkingConfig: {\n                thinkingBudget: 256,\n                includeThoughts: false\n            }\n        } : {\n            thinkingConfig: {\n                thinkingBudget: 0\n            }\n        };\n        const result = await this.generateContent(prompt, {\n            temperature: 0.1,\n            maxOutputTokens: 50,\n            ...config\n        });\n        return result.response;\n    }\n    async generateYouTubeScript(topic, duration, style, targetAudience, enableThinking = true) {\n        const prompt = `\nCreate a YouTube video script about \"${topic}\".\n\nVideo Duration: ${duration}\nStyle: ${style}\nTarget Audience: ${targetAudience}\n\nRequirements:\n- Include compelling hook in first 15 seconds\n- Clear structure with timestamps\n- Engaging storytelling throughout\n- Include call-to-action for likes, subscribes, comments\n- Natural speaking rhythm and flow\n- Include cues for visuals/graphics where appropriate\n- End with strong conclusion and next video teaser\n\nFormat:\n[HOOK - 0:00-0:15]\n[INTRODUCTION - 0:15-0:45]\n[MAIN CONTENT - Sections with timestamps]\n[CONCLUSION & CTA - Final section]\n\nCreate a script that keeps viewers engaged throughout the entire video.\n`;\n        const config = enableThinking ? {\n            thinkingConfig: {\n                thinkingBudget: 2048,\n                includeThoughts: false\n            }\n        } : {\n            thinkingConfig: {\n                thinkingBudget: 0\n            }\n        };\n        const result = await this.generateContent(prompt, {\n            temperature: 0.7,\n            maxOutputTokens: 5000,\n            ...config\n        });\n        return result.response;\n    }\n    async extractKeywordsFromContent(content, enableThinking = false) {\n        const prompt = `\nAnalyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:\n\nContent:\n${content.substring(0, 3000)}\n\nRules:\n- Extract 8-12 high-value keywords and phrases\n- Focus on terms that appear frequently and seem important\n- Include both single keywords and 2-3 word phrases\n- Prioritize terms that would be good for SEO targeting\n- Separate keywords with commas\n- Don't include common words like \"the\", \"and\", \"or\", etc.\n\nReturn only the keywords separated by commas:\n`;\n        const config = enableThinking ? {\n            thinkingConfig: {\n                thinkingBudget: 1024,\n                includeThoughts: false\n            }\n        } : {\n            thinkingConfig: {\n                thinkingBudget: 0\n            }\n        };\n        const result = await this.generateContent(prompt, {\n            temperature: 0.2,\n            maxOutputTokens: 200,\n            ...config\n        });\n        return result.response;\n    }\n    /**\n   * Calculate estimated cost for Enhanced Invincible generation\n   */ static calculateEnhancedInvincibleCost(options) {\n        // Estimate input tokens (research data + prompts + context)\n        const basePromptTokens = 2000; // Enhanced prompts are substantial\n        const researchTokens = options.researchSources * 800; // ~800 tokens per source\n        const competitorTokens = 5 * 1200; // 5 competitors at ~1200 tokens each\n        const estimatedInputTokens = basePromptTokens + researchTokens + competitorTokens;\n        // Estimate output tokens based on word count (1 token ≈ 0.75 words)\n        const estimatedOutputTokens = Math.ceil(options.articleWordCount / 0.75);\n        // Estimate thinking tokens (dynamic thinking uses 10-30% of output tokens)\n        const estimatedThinkingTokens = options.withThinking ? Math.ceil(estimatedOutputTokens * 0.2) : 0;\n        // Calculate costs (Gemini 2.5 Flash-Lite Preview 2025 pricing)\n        const inputCost = estimatedInputTokens * 0.0000001; // $0.10 per million\n        const outputCost = estimatedOutputTokens * 0.0000004; // $0.40 per million\n        const thinkingCost = estimatedThinkingTokens * 0.0000004; // Included in output rate\n        const totalCost = inputCost + outputCost + thinkingCost;\n        return {\n            estimatedInputTokens,\n            estimatedOutputTokens,\n            estimatedThinkingTokens,\n            totalCost,\n            breakdown: {\n                inputCost,\n                outputCost,\n                thinkingCost\n            }\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUNYRixnQkFBZ0JFLE1BQU0sSUFDdEIsSUFBSUgsd0RBQVlBLENBQUM7SUFDZkksS0FBSztRQUFDO0tBQVE7QUFDaEIsR0FBRTtBQUVKLElBQUlDLElBQXFDLEVBQUVKLGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9saWIvcHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID1cbiAgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/P1xuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IFsncXVlcnknXSxcbiAgfSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWEgIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/quota.ts":
/*!**************************!*\
  !*** ./src/lib/quota.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QUOTA_LIMITS: () => (/* binding */ QUOTA_LIMITS),\n/* harmony export */   QuotaManager: () => (/* binding */ QuotaManager)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\nconst QUOTA_LIMITS = {\n    free: {\n        blog_posts: 5,\n        emails: 10,\n        social_media: 20,\n        youtube_scripts: 3,\n        invincible_research: 2\n    },\n    pro: {\n        blog_posts: 50,\n        emails: 100,\n        social_media: 200,\n        youtube_scripts: 25,\n        invincible_research: 20\n    },\n    enterprise: {\n        blog_posts: -1,\n        emails: -1,\n        social_media: -1,\n        youtube_scripts: -1,\n        invincible_research: -1\n    }\n};\nclass QuotaManager {\n    static async checkQuota(userId, quotaType) {\n        try {\n            // Get user's subscription\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: userId\n                },\n                include: {\n                    subscription: true,\n                    quotas: {\n                        where: {\n                            quotaType\n                        }\n                    }\n                }\n            });\n            if (!user) {\n                throw new Error('User not found');\n            }\n            const plan = user.subscription?.plan || 'free';\n            const quota = user.quotas[0];\n            if (!quota) {\n                // Create quota if it doesn't exist\n                const limit = QUOTA_LIMITS[plan][quotaType];\n                const newQuota = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userQuota.create({\n                    data: {\n                        userId,\n                        quotaType,\n                        totalLimit: limit,\n                        resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)\n                    }\n                });\n                return {\n                    hasQuota: limit === -1 || newQuota.used < limit,\n                    used: newQuota.used,\n                    limit,\n                    resetDate: newQuota.resetDate\n                };\n            }\n            // Check if quota needs to be reset\n            if (new Date() >= quota.resetDate) {\n                await this.resetQuota(userId, quotaType);\n                const limit = QUOTA_LIMITS[plan][quotaType];\n                return {\n                    hasQuota: limit === -1 || 0 < limit,\n                    used: 0,\n                    limit,\n                    resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)\n                };\n            }\n            const limit = QUOTA_LIMITS[plan][quotaType];\n            return {\n                hasQuota: limit === -1 || quota.used < limit,\n                used: quota.used,\n                limit,\n                resetDate: quota.resetDate\n            };\n        } catch (error) {\n            console.error('Error checking quota:', error);\n            throw error;\n        }\n    }\n    static async useQuota(userId, quotaType) {\n        try {\n            const quotaCheck = await this.checkQuota(userId, quotaType);\n            if (!quotaCheck.hasQuota) {\n                return false;\n            }\n            // Increment usage\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userQuota.update({\n                where: {\n                    userId_quotaType: {\n                        userId,\n                        quotaType\n                    }\n                },\n                data: {\n                    used: {\n                        increment: 1\n                    }\n                }\n            });\n            // Log usage\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.usageHistory.create({\n                data: {\n                    userId,\n                    action: 'content_generated',\n                    type: quotaType,\n                    metadata: JSON.stringify({\n                        quotaUsed: quotaCheck.used + 1,\n                        quotaLimit: quotaCheck.limit\n                    })\n                }\n            });\n            return true;\n        } catch (error) {\n            console.error('Error using quota:', error);\n            return false;\n        }\n    }\n    static async resetQuota(userId, quotaType) {\n        try {\n            const nextMonth = new Date();\n            nextMonth.setMonth(nextMonth.getMonth() + 1, 1);\n            nextMonth.setHours(0, 0, 0, 0);\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userQuota.update({\n                where: {\n                    userId_quotaType: {\n                        userId,\n                        quotaType\n                    }\n                },\n                data: {\n                    used: 0,\n                    resetDate: nextMonth\n                }\n            });\n        } catch (error) {\n            console.error('Error resetting quota:', error);\n            throw error;\n        }\n    }\n    static async getAllQuotas(userId) {\n        try {\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: userId\n                },\n                include: {\n                    subscription: true,\n                    quotas: true\n                }\n            });\n            if (!user) {\n                throw new Error('User not found');\n            }\n            const plan = user.subscription?.plan || 'free';\n            const quotaTypes = [\n                'blog_posts',\n                'emails',\n                'social_media',\n                'youtube_scripts',\n                'invincible_research'\n            ];\n            const quotas = await Promise.all(quotaTypes.map(async (quotaType)=>{\n                const quotaCheck = await this.checkQuota(userId, quotaType);\n                return {\n                    quotaType,\n                    used: quotaCheck.used,\n                    limit: quotaCheck.limit,\n                    resetDate: quotaCheck.resetDate,\n                    percentage: quotaCheck.limit === -1 ? 0 : quotaCheck.used / quotaCheck.limit * 100\n                };\n            }));\n            return quotas;\n        } catch (error) {\n            console.error('Error getting all quotas:', error);\n            throw error;\n        }\n    }\n    static async upgradeUserPlan(userId, newPlan) {\n        try {\n            // Update subscription\n            await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.subscription.upsert({\n                where: {\n                    userId\n                },\n                update: {\n                    plan: newPlan,\n                    status: 'active'\n                },\n                create: {\n                    userId,\n                    plan: newPlan,\n                    status: 'active'\n                }\n            });\n            // Update all quotas with new limits\n            const quotaTypes = [\n                'blog_posts',\n                'emails',\n                'social_media',\n                'youtube_scripts',\n                'invincible_research'\n            ];\n            for (const quotaType of quotaTypes){\n                const newLimit = QUOTA_LIMITS[newPlan][quotaType];\n                await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userQuota.upsert({\n                    where: {\n                        userId_quotaType: {\n                            userId,\n                            quotaType\n                        }\n                    },\n                    update: {\n                        totalLimit: newLimit\n                    },\n                    create: {\n                        userId,\n                        quotaType,\n                        totalLimit: newLimit,\n                        resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('Error upgrading user plan:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/quota.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/@auth","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fblog-generator%2Fgenerate%2Froute&page=%2Fapi%2Fblog-generator%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog-generator%2Fgenerate%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();